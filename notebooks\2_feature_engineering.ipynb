{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fc4e98e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Preprocessor saved to ../data/model/feature_encoder.pkl\n", "Expected feature columns: ['age', 'readmission', 'numIntervention', 'duree_sejour', 'sexe', 'type_sanguin', 'maladie', 'id_service', 'medecin_traitant', 'personnel', 'id_medicament', 'TrancheAge']\n"]}], "source": ["# 📁 notebooks/2_feature_engineering.ipynb\n", "\n", "import pandas as pd\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "import joblib\n", "\n", "# Load cleaned data\n", "df = pd.read_csv('../data/cleaned_data.csv')\n", "\n", "# Encode target\n", "df['mortalite'] = df['mortalite'].map({'oui': 1, 'non': 0})\n", "\n", "# Define feature columns\n", "categorical_cols = ['sexe', 'type_sanguin', 'maladie', 'id_service', 'medecin_traitant', 'personnel', 'id_medicament', 'TrancheAge']\n", "numerical_cols = ['age', 'readmission', 'numIntervention', 'duree_sejour']\n", "\n", "# Define preprocessor\n", "preprocessor = ColumnTransformer(\n", "    transformers=[\n", "        ('num', StandardScaler(), numerical_cols),\n", "        ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_cols)\n", "    ]\n", ")\n", "\n", "# Apply preprocessor\n", "X = df[numerical_cols + categorical_cols]\n", "X_transformed = preprocessor.fit_transform(X)\n", "\n", "# Save transformed features\n", "pd.DataFrame(X_transformed.toarray()).to_csv('../data/features.csv', index=False)\n", "\n", "# Save preprocessor\n", "joblib.dump(preprocessor, '../data/model/feature_encoder.pkl')\n", "\n", "print(\"✅ Preprocessor saved to ../data/model/feature_encoder.pkl\")\n", "print(\"Expected feature columns:\", numerical_cols + categorical_cols)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}