{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a6b47a4f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns in Excel file: ['cin', 'nom', 'prenom', 'mortalite', 'age', 'sexe', 'type_sanguin', 'maladie', 'date_admission', 'date_sortie', 'readmission', 'medecin_traitant', 'id_service', 'numIntervention', 'personnel', 'id_medicament']\n", "⚠️ Warning: 'date_admission' or 'date_sortie' not found. Setting 'duree_sejour' to 0.\n", "✅ Cleaned data saved to ../data/cleaned_data.csv\n", "Columns in cleaned data: ['age', 'sexe', 'type_sanguin', 'maladie', 'id_service', 'medecin_traitant', 'personnel', 'id_medicament', 'readmission', 'numIntervention', 'duree_sejour', 'TrancheAge', 'mortalite']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>sexe</th>\n", "      <th>type_sanguin</th>\n", "      <th>maladie</th>\n", "      <th>id_service</th>\n", "      <th>medecin_traitant</th>\n", "      <th>personnel</th>\n", "      <th>id_medicament</th>\n", "      <th>readmission</th>\n", "      <th>numIntervention</th>\n", "      <th>duree_sejour</th>\n", "      <th>TrancheAge</th>\n", "      <th>mortalite</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>56</td>\n", "      <td>F</td>\n", "      <td>B-</td>\n", "      <td>Maladie liée à Pédiatre</td>\n", "      <td>ser-100</td>\n", "      <td>med-3615</td>\n", "      <td>pers-4637</td>\n", "      <td>MED-507</td>\n", "      <td>2</td>\n", "      <td>2020</td>\n", "      <td>0</td>\n", "      <td>40-59</td>\n", "      <td>non</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>13</td>\n", "      <td>F</td>\n", "      <td>B+</td>\n", "      <td>Maladie liée à Pédiatre</td>\n", "      <td>ser-101</td>\n", "      <td>med-4814</td>\n", "      <td>pers-8303</td>\n", "      <td>MED-606</td>\n", "      <td>10</td>\n", "      <td>9498</td>\n", "      <td>0</td>\n", "      <td>0-17</td>\n", "      <td>oui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>56</td>\n", "      <td>M</td>\n", "      <td>O+</td>\n", "      <td>Maladie liée à Gastro-entérologue</td>\n", "      <td>ser-102</td>\n", "      <td>med-5012</td>\n", "      <td>pers-7408</td>\n", "      <td>MED-445</td>\n", "      <td>9</td>\n", "      <td>6325</td>\n", "      <td>0</td>\n", "      <td>40-59</td>\n", "      <td>non</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>69</td>\n", "      <td>F</td>\n", "      <td>A-</td>\n", "      <td>Maladie liée à Généraliste</td>\n", "      <td>ser-103</td>\n", "      <td>med-9830</td>\n", "      <td>pers-9199</td>\n", "      <td>MED-972</td>\n", "      <td>10</td>\n", "      <td>9609</td>\n", "      <td>0</td>\n", "      <td>60-79</td>\n", "      <td>oui</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>45</td>\n", "      <td>F</td>\n", "      <td>O+</td>\n", "      <td>Maladie liée à Pédiatre</td>\n", "      <td>ser-104</td>\n", "      <td>med-3803</td>\n", "      <td>pers-6651</td>\n", "      <td>MED-147</td>\n", "      <td>2</td>\n", "      <td>6140</td>\n", "      <td>0</td>\n", "      <td>40-59</td>\n", "      <td>non</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age sexe type_sanguin                            maladie id_service  \\\n", "0   56    F           B-            Maladie liée à Pédiatre    ser-100   \n", "1   13    F           B+            Maladie liée à Pédiatre    ser-101   \n", "2   56    M           O+  Maladie liée à Gastro-entérologue    ser-102   \n", "3   69    F           A-         Maladie liée à Généraliste    ser-103   \n", "4   45    F           O+            Maladie liée à Pédiatre    ser-104   \n", "\n", "  medecin_traitant  personnel id_medicament  readmission  numIntervention  \\\n", "0         med-3615  pers-4637       MED-507            2             2020   \n", "1         med-4814  pers-8303       MED-606           10             9498   \n", "2         med-5012  pers-7408       MED-445            9             6325   \n", "3         med-9830  pers-9199       MED-972           10             9609   \n", "4         med-3803  pers-6651       MED-147            2             6140   \n", "\n", "   duree_sejour TrancheAge mortalite  \n", "0             0      40-59       non  \n", "1             0       0-17       oui  \n", "2             0      40-59       non  \n", "3             0      60-79       oui  \n", "4             0      40-59       non  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# 📁 notebooks/1_data_cleaning.ipynb\n", "\n", "import pandas as pd\n", "\n", "# Load data\n", "df = pd.read_excel('../data/BaseMedicale_Talend.xlsx', sheet_name='Patients')\n", "\n", "# Print column names for debugging\n", "print(\"Columns in Excel file:\", df.columns.tolist())\n", "\n", "# Define expected columns to drop\n", "columns_to_drop = ['cin', 'nom', 'prenom', 'date_admission', 'date_sortie']\n", "\n", "# Drop unnecessary columns if they exist\n", "df = df.drop(columns=[col for col in columns_to_drop if col in df.columns], errors='ignore')\n", "\n", "# Create duree_sejour if date columns exist\n", "if 'date_admission' in df.columns and 'date_sortie' in df.columns:\n", "    df['duree_sejour'] = (pd.to_datetime(df['date_sortie']) - pd.to_datetime(df['date_admission'])).dt.days\n", "else:\n", "    print(\"⚠️ Warning: 'date_admission' or 'date_sortie' not found. Setting 'duree_sejour' to 0.\")\n", "    df['duree_sejour'] = 0\n", "\n", "# Convert numIntervention to numeric if it exists\n", "if 'numIntervention' in df.columns:\n", "    df['numIntervention'] = df['numIntervention'].apply(lambda x: int(x.replace('int-', '')) if isinstance(x, str) else x)\n", "else:\n", "    print(\"⚠️ Warning: 'numIntervention' not found. Setting to 0.\")\n", "    df['numIntervention'] = 0\n", "\n", "# Create TrancheAge if age exists\n", "if 'age' in df.columns:\n", "    bins = [0, 18, 40, 60, 80, 120]\n", "    labels = [\"0-17\", \"18-39\", \"40-59\", \"60-79\", \"80+\"]\n", "    df['TrancheAge'] = pd.cut(df['age'], bins=bins, labels=labels, right=False)\n", "else:\n", "    print(\"⚠️ Warning: 'age' not found. Setting 'TrancheAge' to 'inconnu'.\")\n", "    df['TrancheAge'] = 'inconnu'\n", "\n", "# Define expected columns (excluding mortalite, which is the target)\n", "expected_columns = ['age', 'sexe', 'type_sanguin', 'maladie', 'id_service', \n", "                   'medecin_traitant', 'personnel', 'id_medicament', \n", "                   'readmission', 'numIntervention', 'duree_sejour', 'TrancheAge', 'mortalite']\n", "\n", "# Add missing columns with default values\n", "for col in expected_columns:\n", "    if col not in df.columns:\n", "        if col in ['age', 'readmission', 'numIntervention', 'duree_sejour']:\n", "            df[col] = 0\n", "        elif col == 'mortalite':\n", "            df[col] = 'non'  # Default value for target\n", "        else:\n", "            df[col] = 'inconnu'\n", "\n", "# Keep only expected columns\n", "df = df[expected_columns]\n", "\n", "# Save cleaned data\n", "df.to_csv('../data/cleaned_data.csv', index=False)\n", "\n", "print(\"✅ Cleaned data saved to ../data/cleaned_data.csv\")\n", "print(\"Columns in cleaned data:\", df.columns.tolist())\n", "df.head()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}