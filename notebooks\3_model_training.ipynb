{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5144a591", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Model saved to ../data/model/random_forest_model.pkl\n"]}], "source": ["# 📁 notebooks/3_model_training.ipynb\n", "\n", "import pandas as pd\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "import joblib\n", "\n", "# Load cleaned data\n", "df = pd.read_csv('../data/cleaned_data.csv')\n", "\n", "# Separate features and target\n", "X = df.drop('mortalite', axis=1)\n", "y = df['mortalite']\n", "\n", "# Load preprocessor\n", "preprocessor = joblib.load('../data/model/feature_encoder.pkl')\n", "\n", "# Transform data\n", "X_transformed = preprocessor.transform(X)\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(X_transformed, y, test_size=0.2, random_state=42)\n", "\n", "# Train model\n", "model = RandomForestClassifier(random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "# Save model\n", "joblib.dump(model, '../data/model/random_forest_model.pkl')\n", "\n", "print(\"✅ Model saved to ../data/model/random_forest_model.pkl\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}