
# This file was generated by 'versioneer.py' (0.23) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-07-08T10:47:47-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "ea1dd9a838749a58bcc2d026693cc5a3c3cdbad1",
 "version": "1.8.15"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
