import streamlit as st
from src.model import predict_patient

st.set_page_config(page_title="Prédiction Mortalité", layout="centered")

st.title("Prédiction de la Mortalité")
st.markdown("Entrez les informations pour obtenir la probabilité de mortalité (%).")

# Dictionnaire de mappage : noms réels -> codes
medicament_mapping = {
    "Paracétamol": "MED001",
    "Ibuprofène": "MED002",
    "Loratadine": "MED003",
    "Amoxicilline": "MED004",
    "VentoliAne": "MED005",
    "Zyrtec": "MED006",
    "Autre": "Autre"
}

# Form
with st.form("patient_form"):
    nom = st.text_input("Nom du patient", placeholder="Entrez le nom du patient")
    sexe = st.selectbox("Sexe", ["Homme", "Femme"])
    age = st.number_input("Âge", min_value=0, max_value=120, value=30)
    type_sanguin = st.selectbox("Type sanguin", ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"])
    maladie = st.selectbox("Maladie", ["diabète", "hypertension", "asthme", "cancer", "autre"])
    medicaments = st.multiselect("Médicaments pris", list(medicament_mapping.keys()), placeholder="Sélectionnez les médicaments")
    readmission = st.number_input("Nombre de réadmissions", min_value=0, max_value=10, value=0)
    duree_sejour = st.number_input("Durée de séjour (jours)", min_value=0, value=0)

    # Submit button
    submitted = st.form_submit_button("Prédire")

    if submitted:
        if not nom.strip() or not medicaments:
            st.warning("❌ Veuillez remplir les champs obligatoires (Nom, Médicaments).")
        else:
            # Convertir les noms réels en codes pour le modèle
            medicaments_codes = [medicament_mapping[med] for med in medicaments]
            
            # Input dictionary
            input_data = {
                "nom": nom.strip(),
                "sexe": sexe,
                "age": age,
                "type_sanguin": type_sanguin,
                "maladie": maladie,
                "id_medicament": ",".join(medicaments_codes),
                "readmission": readmission,
                "duree_sejour": duree_sejour
            }

            # Predict
            resultat = predict_patient(input_data)
            if "Erreur" in resultat:
                st.error(resultat)
            else:
                st.success(f"Probabilité de mortalité de {nom.strip().capitalize()} est de : {resultat}%")

                