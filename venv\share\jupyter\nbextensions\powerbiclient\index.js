/*! For license information please see index.js.LICENSE.txt */
define(["@jupyter-widgets/base"],(t=>(()=>{var e={206:(t,e,r)=>{(e=r(495)(!1)).push([t.id,"/* Copyright (c) Microsoft Corporation.\r\n   Licensed under the MIT license. */\r\n\r\n/* Add custom CSS here */\r\n",""]),t.exports=e},495:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r=function(t,e){var r,i,a,o=t[1]||"",n=t[3];if(!n)return o;if(e&&"function"==typeof btoa){var l=(r=n,i=btoa(unescape(encodeURIComponent(JSON.stringify(r)))),a="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(i),"/*# ".concat(a," */")),s=n.sources.map((function(t){return"/*# sourceURL=".concat(n.sourceRoot||"").concat(t," */")}));return[o].concat(s).concat([l]).join("\n")}return[o].join("\n")}(e,t);return e[2]?"@media ".concat(e[2]," {").concat(r,"}"):r})).join("")},e.i=function(t,r,i){"string"==typeof t&&(t=[[null,t,""]]);var a={};if(i)for(var o=0;o<this.length;o++){var n=this[o][0];null!=n&&(a[n]=!0)}for(var l=0;l<t.length;l++){var s=[].concat(t[l]);i&&a[s[0]]||(r&&(s[2]?s[2]="".concat(r," and ").concat(s[2]):s[2]=r),e.push(s))}},e}},957:function(t){var e;e=()=>(()=>{var t={"./node_modules/http-post-message/dist/httpPostMessage.js":function(t){var e;e=function(){return function(t){var e={};function r(i){if(e[i])return e[i].exports;var a=e[i]={exports:{},id:i,loaded:!1};return t[i].call(a.exports,a,a.exports,r),a.loaded=!0,a.exports}return r.m=t,r.c=e,r.p="",r(0)}([function(t,e){"use strict";var r=function(){function t(t,e,r){void 0===e&&(e={}),this.defaultHeaders=e,this.defaultTargetWindow=r,this.windowPostMessageProxy=t}return t.addTrackingProperties=function(t,e){return t.headers=t.headers||{},e&&e.id&&(t.headers.id=e.id),t},t.getTrackingProperties=function(t){return{id:t.headers&&t.headers.id}},t.isErrorMessage=function(t){return"number"==typeof(t&&t.statusCode)&&!(200<=t.statusCode&&t.statusCode<300)},t.prototype.get=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=this.defaultTargetWindow),this.send({method:"GET",url:t,headers:e},r)},t.prototype.post=function(t,e,r,i){return void 0===r&&(r={}),void 0===i&&(i=this.defaultTargetWindow),this.send({method:"POST",url:t,headers:r,body:e},i)},t.prototype.put=function(t,e,r,i){return void 0===r&&(r={}),void 0===i&&(i=this.defaultTargetWindow),this.send({method:"PUT",url:t,headers:r,body:e},i)},t.prototype.patch=function(t,e,r,i){return void 0===r&&(r={}),void 0===i&&(i=this.defaultTargetWindow),this.send({method:"PATCH",url:t,headers:r,body:e},i)},t.prototype.delete=function(t,e,r,i){return void 0===e&&(e=null),void 0===r&&(r={}),void 0===i&&(i=this.defaultTargetWindow),this.send({method:"DELETE",url:t,headers:r,body:e},i)},t.prototype.send=function(t,e){if(void 0===e&&(e=this.defaultTargetWindow),t.headers=this.assign({},this.defaultHeaders,t.headers),!e)throw new Error("target window is not provided.  You must either provide the target window explicitly as argument to request, or specify default target window when constructing instance of this class.");return this.windowPostMessageProxy.postMessage(e,t)},t.prototype.assign=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(null==t)throw new TypeError("Cannot convert undefined or null to object");var i=Object(t);return e.forEach((function(t){if(null!=t)for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&(i[e]=t[e])})),i},t}();e.HttpPostMessage=r}])},t.exports=e()},"./node_modules/powerbi-models/dist/models.js":function(t){var e;e=()=>(()=>{var t=[function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.TextAlignment=e.CommonErrorCodes=e.BookmarksPlayMode=e.ExportDataType=e.QnaMode=e.PageNavigationPosition=e.BrowserPrintAdjustmentsMode=e.AggregateFunction=e.DataCacheMode=e.CredentialType=e.isPercentOfGrandTotal=e.isColumnAggr=e.isHierarchyLevelAggr=e.isHierarchyLevel=e.isColumn=e.isMeasure=e.getFilterType=e.isBasicFilterWithKeys=e.isFilterKeyColumnsTarget=e.HierarchyFilter=e.AdvancedFilter=e.TupleFilter=e.IdentityFilter=e.BasicFilterWithKeys=e.BasicFilter=e.RelativeTimeFilter=e.RelativeDateFilter=e.TopNFilter=e.IncludeExcludeFilter=e.NotSupportedFilter=e.Filter=e.RelativeDateOperators=e.RelativeDateFilterTimeUnit=e.FilterType=e.FiltersLevel=e.FiltersOperations=e.MenuLocation=e.ContrastMode=e.TokenType=e.ViewMode=e.Permissions=e.SectionVisibility=e.ReportAlignment=e.HyperlinkClickBehavior=e.LayoutType=e.VisualContainerDisplayMode=e.BackgroundType=e.DisplayOption=e.PageSizeType=e.TraceType=void 0,e.validateVisualHeader=e.validateExportDataRequest=e.validateQnaInterpretInputData=e.validateLoadQnaConfiguration=e.validateSaveAsParameters=e.validateUpdateFiltersRequest=e.validateFilter=e.validatePage=e.validateTileLoad=e.validateDashboardLoad=e.validateQuickCreate=e.validateCreateReport=e.validatePaginatedReportLoad=e.validateReportLoad=e.validateMenuGroupExtension=e.validateExtension=e.validateCustomPageSize=e.validateVisualizationsPane=e.validateSyncSlicersPane=e.validateSelectionPane=e.validatePageNavigationPane=e.validateFieldsPane=e.validateFiltersPane=e.validateBookmarksPane=e.validatePanes=e.validateSettings=e.validateCaptureBookmarkRequest=e.validateApplyBookmarkStateRequest=e.validateApplyBookmarkByNameRequest=e.validateAddBookmarkRequest=e.validatePlayBookmarkRequest=e.validateSlicerState=e.validateSlicer=e.validateVisualSelector=e.isIExtensionArray=e.isIExtensions=e.isGroupedMenuExtension=e.isFlatMenuExtension=e.isReportFiltersArray=e.isOnLoadFilters=e.VisualDataRoleKindPreference=e.VisualDataRoleKind=e.CommandDisplayOption=e.SlicerTargetSelector=e.VisualTypeSelector=e.VisualSelector=e.PageSelector=e.Selector=e.SortDirection=e.LegendPosition=void 0,e.validatePrintSettings=e.validateZoomLevel=e.validateCustomTheme=e.validateCommandsSettings=e.validateVisualSettings=void 0;var o,n,l,s,d,u,c,p,f,h,v,y,m,g,V,b,w,_,O,P=r(1);(O=e.TraceType||(e.TraceType={}))[O.Information=0]="Information",O[O.Verbose=1]="Verbose",O[O.Warning=2]="Warning",O[O.Error=3]="Error",O[O.ExpectedError=4]="ExpectedError",O[O.UnexpectedError=5]="UnexpectedError",O[O.Fatal=6]="Fatal",(_=e.PageSizeType||(e.PageSizeType={}))[_.Widescreen=0]="Widescreen",_[_.Standard=1]="Standard",_[_.Cortana=2]="Cortana",_[_.Letter=3]="Letter",_[_.Custom=4]="Custom",_[_.Mobile=5]="Mobile",(w=e.DisplayOption||(e.DisplayOption={}))[w.FitToPage=0]="FitToPage",w[w.FitToWidth=1]="FitToWidth",w[w.ActualSize=2]="ActualSize",(b=e.BackgroundType||(e.BackgroundType={}))[b.Default=0]="Default",b[b.Transparent=1]="Transparent",(V=e.VisualContainerDisplayMode||(e.VisualContainerDisplayMode={}))[V.Visible=0]="Visible",V[V.Hidden=1]="Hidden",(g=e.LayoutType||(e.LayoutType={}))[g.Master=0]="Master",g[g.Custom=1]="Custom",g[g.MobilePortrait=2]="MobilePortrait",g[g.MobileLandscape=3]="MobileLandscape",(m=e.HyperlinkClickBehavior||(e.HyperlinkClickBehavior={}))[m.Navigate=0]="Navigate",m[m.NavigateAndRaiseEvent=1]="NavigateAndRaiseEvent",m[m.RaiseEvent=2]="RaiseEvent",(y=e.ReportAlignment||(e.ReportAlignment={}))[y.Left=0]="Left",y[y.Center=1]="Center",y[y.Right=2]="Right",y[y.None=3]="None",(v=e.SectionVisibility||(e.SectionVisibility={}))[v.AlwaysVisible=0]="AlwaysVisible",v[v.HiddenInViewMode=1]="HiddenInViewMode",(h=e.Permissions||(e.Permissions={}))[h.Read=0]="Read",h[h.ReadWrite=1]="ReadWrite",h[h.Copy=2]="Copy",h[h.Create=4]="Create",h[h.All=7]="All",(f=e.ViewMode||(e.ViewMode={}))[f.View=0]="View",f[f.Edit=1]="Edit",(p=e.TokenType||(e.TokenType={}))[p.Aad=0]="Aad",p[p.Embed=1]="Embed",(c=e.ContrastMode||(e.ContrastMode={}))[c.None=0]="None",c[c.HighContrast1=1]="HighContrast1",c[c.HighContrast2=2]="HighContrast2",c[c.HighContrastBlack=3]="HighContrastBlack",c[c.HighContrastWhite=4]="HighContrastWhite",(u=e.MenuLocation||(e.MenuLocation={}))[u.Bottom=0]="Bottom",u[u.Top=1]="Top",(d=e.FiltersOperations||(e.FiltersOperations={}))[d.RemoveAll=0]="RemoveAll",d[d.ReplaceAll=1]="ReplaceAll",d[d.Add=2]="Add",d[d.Replace=3]="Replace",(s=e.FiltersLevel||(e.FiltersLevel={}))[s.Report=0]="Report",s[s.Page=1]="Page",s[s.Visual=2]="Visual",function(t){t[t.Advanced=0]="Advanced",t[t.Basic=1]="Basic",t[t.Unknown=2]="Unknown",t[t.IncludeExclude=3]="IncludeExclude",t[t.RelativeDate=4]="RelativeDate",t[t.TopN=5]="TopN",t[t.Tuple=6]="Tuple",t[t.RelativeTime=7]="RelativeTime",t[t.Identity=8]="Identity",t[t.Hierarchy=9]="Hierarchy"}(o=e.FilterType||(e.FilterType={})),(l=e.RelativeDateFilterTimeUnit||(e.RelativeDateFilterTimeUnit={}))[l.Days=0]="Days",l[l.Weeks=1]="Weeks",l[l.CalendarWeeks=2]="CalendarWeeks",l[l.Months=3]="Months",l[l.CalendarMonths=4]="CalendarMonths",l[l.Years=5]="Years",l[l.CalendarYears=6]="CalendarYears",l[l.Minutes=7]="Minutes",l[l.Hours=8]="Hours",(n=e.RelativeDateOperators||(e.RelativeDateOperators={}))[n.InLast=0]="InLast",n[n.InThis=1]="InThis",n[n.InNext=2]="InNext";var S=function(){function t(t,e){this.target=t,this.filterType=e}return t.prototype.toJSON=function(){var t={$schema:this.schemaUrl,target:this.target,filterType:this.filterType};return void 0!==this.displaySettings&&(t.displaySettings=this.displaySettings),t},t}();e.Filter=S;var T=function(t){function e(r,i,a){var n=t.call(this,r,o.Unknown)||this;return n.message=i,n.notSupportedTypeName=a,n.schemaUrl=e.schemaUrl,n}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.message=this.message,e.notSupportedTypeName=this.notSupportedTypeName,e},e.schemaUrl="http://powerbi.com/product/schema#notSupported",e}(S);e.NotSupportedFilter=T;var E=function(t){function e(r,i,a){var n=t.call(this,r,o.IncludeExclude)||this;return n.values=a,n.isExclude=i,n.schemaUrl=e.schemaUrl,n}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.isExclude=this.isExclude,e.values=this.values,e},e.schemaUrl="http://powerbi.com/product/schema#includeExclude",e}(S);e.IncludeExcludeFilter=E;var C=function(t){function e(r,i,a,n){var l=t.call(this,r,o.TopN)||this;return l.operator=i,l.itemCount=a,l.schemaUrl=e.schemaUrl,l.orderBy=n,l}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.itemCount=this.itemCount,e.orderBy=this.orderBy,e},e.schemaUrl="http://powerbi.com/product/schema#topN",e}(S);e.TopNFilter=C;var k=function(t){function e(r,i,a,n,l){var s=t.call(this,r,o.RelativeDate)||this;return s.operator=i,s.timeUnitsCount=a,s.timeUnitType=n,s.includeToday=l,s.schemaUrl=e.schemaUrl,s}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.timeUnitsCount=this.timeUnitsCount,e.timeUnitType=this.timeUnitType,e.includeToday=this.includeToday,e},e.schemaUrl="http://powerbi.com/product/schema#relativeDate",e}(S);e.RelativeDateFilter=k;var F=function(t){function e(r,i,a,n){var l=t.call(this,r,o.RelativeTime)||this;return l.operator=i,l.timeUnitsCount=a,l.timeUnitType=n,l.schemaUrl=e.schemaUrl,l}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.timeUnitsCount=this.timeUnitsCount,e.timeUnitType=this.timeUnitType,e},e.schemaUrl="http://powerbi.com/product/schema#relativeTime",e}(S);e.RelativeTimeFilter=F;var x=function(t){function e(r,i){for(var a=[],n=2;n<arguments.length;n++)a[n-2]=arguments[n];var l=t.call(this,r,o.Basic)||this;if(l.operator=i,l.schemaUrl=e.schemaUrl,0===a.length&&"All"!==i)throw new Error('values must be a non-empty array unless your operator is "All".');return Array.isArray(a[0])?l.values=a[0]:l.values=a,l}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.values=this.values,e.requireSingleSelection=!!this.requireSingleSelection,e},e.schemaUrl="http://powerbi.com/product/schema#basic",e}(S);e.BasicFilter=x;var R=function(t){function e(e,r,i,a){var o=t.call(this,e,r,i)||this;o.keyValues=a,o.target=e;var n=e.keys?e.keys.length:0;if(n>0&&!a)throw new Error("You should pass the values to be filtered for each key. You passed: no values and ".concat(n," keys"));if(0===n&&a&&a.length>0)throw new Error("You passed key values but your target object doesn't contain the keys to be filtered");for(var l=0,s=o.keyValues;l<s.length;l++){var d=s[l];if(d){var u=d.length;if(u!==n)throw new Error("Each tuple of key values should contain a value for each of the keys. You passed: ".concat(u," values and ").concat(n," keys"))}}return o}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.keyValues=this.keyValues,e},e}(x);e.BasicFilterWithKeys=R;var j=function(t){function e(r,i){var a=t.call(this,r,o.Identity)||this;return a.operator=i,a.schemaUrl=e.schemaUrl,a}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.target=this.target,e},e.schemaUrl="http://powerbi.com/product/schema#identity",e}(S);e.IdentityFilter=j;var A=function(t){function e(r,i,a){var n=t.call(this,r,o.Tuple)||this;return n.operator=i,n.schemaUrl=e.schemaUrl,n.values=a,n}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.values=this.values,e.target=this.target,e},e.schemaUrl="http://powerbi.com/product/schema#tuple",e}(S);e.TupleFilter=A;var M=function(t){function e(r,i){for(var a=[],n=2;n<arguments.length;n++)a[n-2]=arguments[n];var l,s=t.call(this,r,o.Advanced)||this;if(s.schemaUrl=e.schemaUrl,"string"!=typeof i||0===i.length)throw new Error("logicalOperator must be a valid operator, You passed: ".concat(i));if(s.logicalOperator=i,(l=Array.isArray(a[0])?a[0]:a).length>2)throw new Error("AdvancedFilters may not have more than two conditions. You passed: ".concat(a.length));if(1===l.length&&"And"!==i)throw new Error('Logical Operator must be "And" when there is only one condition provided');return s.conditions=l,s}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.logicalOperator=this.logicalOperator,e.conditions=this.conditions,e},e.schemaUrl="http://powerbi.com/product/schema#advanced",e}(S);e.AdvancedFilter=M;var q,L,I,D,N,B,U,H,W,z=function(t){function e(r,i){var a=t.call(this,r,o.Hierarchy)||this;return a.schemaUrl=e.schemaUrl,a.hierarchyData=i,a}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.hierarchyData=this.hierarchyData,e.target=this.target,e},e.schemaUrl="http://powerbi.com/product/schema#hierarchy",e}(S);function Q(t){if(t.filterType)return t.filterType;var e=t,r=t;return"string"==typeof e.operator&&Array.isArray(e.values)?o.Basic:"string"==typeof r.logicalOperator&&Array.isArray(r.conditions)?o.Advanced:o.Unknown}function J(t){return!(!t.table||!t.column||t.aggregationFunction)}e.HierarchyFilter=z,e.isFilterKeyColumnsTarget=function(t){return J(t)&&!!t.keys},e.isBasicFilterWithKeys=function(t){return Q(t)===o.Basic&&!!t.keyValues},e.getFilterType=Q,e.isMeasure=function(t){return void 0!==t.table&&void 0!==t.measure},e.isColumn=J,e.isHierarchyLevel=function(t){return!(!(t.table&&t.hierarchy&&t.hierarchyLevel)||t.aggregationFunction)},e.isHierarchyLevelAggr=function(t){return!!(t.table&&t.hierarchy&&t.hierarchyLevel&&t.aggregationFunction)},e.isColumnAggr=function(t){return!!(t.table&&t.column&&t.aggregationFunction)},e.isPercentOfGrandTotal=function(t){return!!t.percentOfGrandTotal},(W=e.CredentialType||(e.CredentialType={}))[W.NoConnection=0]="NoConnection",W[W.OnBehalfOf=1]="OnBehalfOf",W[W.Anonymous=2]="Anonymous",(H=e.DataCacheMode||(e.DataCacheMode={}))[H.Import=0]="Import",H[H.DirectQuery=1]="DirectQuery",(U=e.AggregateFunction||(e.AggregateFunction={}))[U.Default=1]="Default",U[U.None=2]="None",U[U.Sum=3]="Sum",U[U.Min=4]="Min",U[U.Max=5]="Max",U[U.Count=6]="Count",U[U.Average=7]="Average",U[U.DistinctCount=8]="DistinctCount",(B=e.BrowserPrintAdjustmentsMode||(e.BrowserPrintAdjustmentsMode={}))[B.Default=0]="Default",B[B.NoAdjustments=1]="NoAdjustments",(N=e.PageNavigationPosition||(e.PageNavigationPosition={}))[N.Bottom=0]="Bottom",N[N.Left=1]="Left",(D=e.QnaMode||(e.QnaMode={}))[D.Interactive=0]="Interactive",D[D.ResultOnly=1]="ResultOnly",(I=e.ExportDataType||(e.ExportDataType={}))[I.Summarized=0]="Summarized",I[I.Underlying=1]="Underlying",(L=e.BookmarksPlayMode||(e.BookmarksPlayMode={}))[L.Off=0]="Off",L[L.Presentation=1]="Presentation",e.CommonErrorCodes={TokenExpired:"TokenExpired",NotFound:"PowerBIEntityNotFound",InvalidParameters:"Invalid parameters",LoadReportFailed:"LoadReportFailed",NotAuthorized:"PowerBINotAuthorizedException",FailedToLoadModel:"ExplorationContainer_FailedToLoadModel_DefaultDetails"},e.TextAlignment={Left:"left",Center:"center",Right:"right"},e.LegendPosition={Top:"Top",Bottom:"Bottom",Right:"Right",Left:"Left",TopCenter:"TopCenter",BottomCenter:"BottomCenter",RightCenter:"RightCenter",LeftCenter:"LeftCenter"},(q=e.SortDirection||(e.SortDirection={}))[q.Ascending=1]="Ascending",q[q.Descending=2]="Descending";var G=function(){function t(t){this.$schema=t}return t.prototype.toJSON=function(){return{$schema:this.$schema}},t}();e.Selector=G;var K=function(t){function e(r){var i=t.call(this,e.schemaUrl)||this;return i.pageName=r,i}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.pageName=this.pageName,e},e.schemaUrl="http://powerbi.com/product/schema#pageSelector",e}(G);e.PageSelector=K;var $=function(t){function e(r){var i=t.call(this,e.schemaUrl)||this;return i.visualName=r,i}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.visualName=this.visualName,e},e.schemaUrl="http://powerbi.com/product/schema#visualSelector",e}(G);e.VisualSelector=$;var Y=function(t){function e(e){var r=t.call(this,$.schemaUrl)||this;return r.visualType=e,r}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.visualType=this.visualType,e},e.schemaUrl="http://powerbi.com/product/schema#visualTypeSelector",e}(G);e.VisualTypeSelector=Y;var Z,X,tt,et=function(t){function e(e){var r=t.call(this,$.schemaUrl)||this;return r.target=e,r}return a(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.target=this.target,e},e.schemaUrl="http://powerbi.com/product/schema#slicerTargetSelector",e}(G);function rt(t){return Array.isArray(t)}function it(t){return t&&!!t.groupName}function at(t){return Array.isArray(t)}function ot(t){var e=t.message;return e||(e="".concat(t.path," is invalid. Not meeting ").concat(t.keyword," constraint")),{message:e}}e.SlicerTargetSelector=et,(tt=e.CommandDisplayOption||(e.CommandDisplayOption={}))[tt.Enabled=0]="Enabled",tt[tt.Disabled=1]="Disabled",tt[tt.Hidden=2]="Hidden",(X=e.VisualDataRoleKind||(e.VisualDataRoleKind={}))[X.Grouping=0]="Grouping",X[X.Measure=1]="Measure",X[X.GroupingOrMeasure=2]="GroupingOrMeasure",(Z=e.VisualDataRoleKindPreference||(e.VisualDataRoleKindPreference={}))[Z.Measure=0]="Measure",Z[Z.Grouping=1]="Grouping",e.isOnLoadFilters=function(t){return t&&!rt(t)},e.isReportFiltersArray=rt,e.isFlatMenuExtension=function(t){return t&&!it(t)},e.isGroupedMenuExtension=it,e.isIExtensions=function(t){return t&&!at(t)},e.isIExtensionArray=at,e.validateVisualSelector=function(t){var e=P.Validators.visualSelectorValidator.validate(t);return e?e.map(ot):void 0},e.validateSlicer=function(t){var e=P.Validators.slicerValidator.validate(t);return e?e.map(ot):void 0},e.validateSlicerState=function(t){var e=P.Validators.slicerStateValidator.validate(t);return e?e.map(ot):void 0},e.validatePlayBookmarkRequest=function(t){var e=P.Validators.playBookmarkRequestValidator.validate(t);return e?e.map(ot):void 0},e.validateAddBookmarkRequest=function(t){var e=P.Validators.addBookmarkRequestValidator.validate(t);return e?e.map(ot):void 0},e.validateApplyBookmarkByNameRequest=function(t){var e=P.Validators.applyBookmarkByNameRequestValidator.validate(t);return e?e.map(ot):void 0},e.validateApplyBookmarkStateRequest=function(t){var e=P.Validators.applyBookmarkStateRequestValidator.validate(t);return e?e.map(ot):void 0},e.validateCaptureBookmarkRequest=function(t){var e=P.Validators.captureBookmarkRequestValidator.validate(t);return e?e.map(ot):void 0},e.validateSettings=function(t){var e=P.Validators.settingsValidator.validate(t);return e?e.map(ot):void 0},e.validatePanes=function(t){var e=P.Validators.reportPanesValidator.validate(t);return e?e.map(ot):void 0},e.validateBookmarksPane=function(t){var e=P.Validators.bookmarksPaneValidator.validate(t);return e?e.map(ot):void 0},e.validateFiltersPane=function(t){var e=P.Validators.filtersPaneValidator.validate(t);return e?e.map(ot):void 0},e.validateFieldsPane=function(t){var e=P.Validators.fieldsPaneValidator.validate(t);return e?e.map(ot):void 0},e.validatePageNavigationPane=function(t){var e=P.Validators.pageNavigationPaneValidator.validate(t);return e?e.map(ot):void 0},e.validateSelectionPane=function(t){var e=P.Validators.selectionPaneValidator.validate(t);return e?e.map(ot):void 0},e.validateSyncSlicersPane=function(t){var e=P.Validators.syncSlicersPaneValidator.validate(t);return e?e.map(ot):void 0},e.validateVisualizationsPane=function(t){var e=P.Validators.visualizationsPaneValidator.validate(t);return e?e.map(ot):void 0},e.validateCustomPageSize=function(t){var e=P.Validators.customPageSizeValidator.validate(t);return e?e.map(ot):void 0},e.validateExtension=function(t){var e=P.Validators.extensionValidator.validate(t);return e?e.map(ot):void 0},e.validateMenuGroupExtension=function(t){var e=P.Validators.menuGroupExtensionValidator.validate(t);return e?e.map(ot):void 0},e.validateReportLoad=function(t){var e=P.Validators.reportLoadValidator.validate(t);return e?e.map(ot):void 0},e.validatePaginatedReportLoad=function(t){var e=P.Validators.paginatedReportLoadValidator.validate(t);return e?e.map(ot):void 0},e.validateCreateReport=function(t){var e=P.Validators.reportCreateValidator.validate(t);return e?e.map(ot):void 0},e.validateQuickCreate=function(t){var e=P.Validators.quickCreateValidator.validate(t);return e?e.map(ot):void 0},e.validateDashboardLoad=function(t){var e=P.Validators.dashboardLoadValidator.validate(t);return e?e.map(ot):void 0},e.validateTileLoad=function(t){var e=P.Validators.tileLoadValidator.validate(t);return e?e.map(ot):void 0},e.validatePage=function(t){var e=P.Validators.pageValidator.validate(t);return e?e.map(ot):void 0},e.validateFilter=function(t){var e=P.Validators.filterValidator.validate(t);return e?e.map(ot):void 0},e.validateUpdateFiltersRequest=function(t){var e=P.Validators.updateFiltersRequestValidator.validate(t);return e?e.map(ot):void 0},e.validateSaveAsParameters=function(t){var e=P.Validators.saveAsParametersValidator.validate(t);return e?e.map(ot):void 0},e.validateLoadQnaConfiguration=function(t){var e=P.Validators.loadQnaValidator.validate(t);return e?e.map(ot):void 0},e.validateQnaInterpretInputData=function(t){var e=P.Validators.qnaInterpretInputDataValidator.validate(t);return e?e.map(ot):void 0},e.validateExportDataRequest=function(t){var e=P.Validators.exportDataRequestValidator.validate(t);return e?e.map(ot):void 0},e.validateVisualHeader=function(t){var e=P.Validators.visualHeaderValidator.validate(t);return e?e.map(ot):void 0},e.validateVisualSettings=function(t){var e=P.Validators.visualSettingsValidator.validate(t);return e?e.map(ot):void 0},e.validateCommandsSettings=function(t){var e=P.Validators.commandsSettingsValidator.validate(t);return e?e.map(ot):void 0},e.validateCustomTheme=function(t){var e=P.Validators.customThemeValidator.validate(t);return e?e.map(ot):void 0},e.validateZoomLevel=function(t){var e=P.Validators.zoomLevelValidator.validate(t);return e?e.map(ot):void 0},e.validatePrintSettings=function(t){var e=P.Validators.printSettingsValidator.validate(t);return e?e.map(ot):void 0}},(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Validators=void 0;var i=r(2),a=r(5),o=r(6),n=r(7),l=r(8),s=r(9),d=r(10),u=r(11),c=r(12),p=r(13),f=r(14),h=r(15),v=r(16),y=r(17),m=r(18),g=r(19),V=r(20),b=r(21),w=r(22),_=r(23),O=r(24),P=r(25),S=r(26),T=r(27),E=r(28),C=r(29),k=r(4),F=r(30),x=r(31),R=r(32),j=r(33);e.Validators={addBookmarkRequestValidator:new a.AddBookmarkRequestValidator,advancedFilterTypeValidator:new k.EnumValidator([0]),advancedFilterValidator:new c.AdvancedFilterValidator,anyArrayValidator:new k.ArrayValidator([new S.AnyOfValidator([new k.StringValidator,new k.NumberValidator,new k.BooleanValidator])]),anyFilterValidator:new S.AnyOfValidator([new c.BasicFilterValidator,new c.AdvancedFilterValidator,new c.IncludeExcludeFilterValidator,new c.NotSupportedFilterValidator,new c.RelativeDateFilterValidator,new c.TopNFilterValidator,new c.RelativeTimeFilterValidator,new c.HierarchyFilterValidator]),anyValueValidator:new S.AnyOfValidator([new k.StringValidator,new k.NumberValidator,new k.BooleanValidator]),actionBarValidator:new i.ActionBarValidator,statusBarValidator:new i.StatusBarValidator,applyBookmarkByNameRequestValidator:new a.ApplyBookmarkByNameRequestValidator,applyBookmarkStateRequestValidator:new a.ApplyBookmarkStateRequestValidator,applyBookmarkValidator:new S.AnyOfValidator([new a.ApplyBookmarkByNameRequestValidator,new a.ApplyBookmarkStateRequestValidator]),backgroundValidator:new k.EnumValidator([0,1]),basicFilterTypeValidator:new k.EnumValidator([1]),basicFilterValidator:new c.BasicFilterValidator,booleanArrayValidator:new k.BooleanArrayValidator,booleanValidator:new k.BooleanValidator,bookmarksPaneValidator:new h.BookmarksPaneValidator,captureBookmarkOptionsValidator:new a.CaptureBookmarkOptionsValidator,captureBookmarkRequestValidator:new a.CaptureBookmarkRequestValidator,columnSchemaArrayValidator:new k.ArrayValidator([new x.ColumnSchemaValidator]),commandDisplayOptionValidator:new k.EnumValidator([0,1,2]),commandExtensionSelectorValidator:new S.AnyOfValidator([new b.VisualSelectorValidator,new b.VisualTypeSelectorValidator]),commandExtensionArrayValidator:new k.ArrayValidator([new u.CommandExtensionValidator]),commandExtensionValidator:new u.CommandExtensionValidator,commandsSettingsArrayValidator:new k.ArrayValidator([new o.CommandsSettingsValidator]),commandsSettingsValidator:new o.CommandsSettingsValidator,conditionItemValidator:new c.ConditionItemValidator,contrastModeValidator:new k.EnumValidator([0,1,2,3,4]),credentialDetailsValidator:new C.MapValidator([new k.StringValidator],[new k.StringValidator]),credentialsValidator:new x.CredentialsValidator,credentialTypeValidator:new k.EnumValidator([0,1,2]),customLayoutDisplayOptionValidator:new k.EnumValidator([0,1,2]),customLayoutValidator:new p.CustomLayoutValidator,customPageSizeValidator:new f.CustomPageSizeValidator,customThemeValidator:new n.CustomThemeValidator,dashboardLoadValidator:new l.DashboardLoadValidator,dataCacheModeValidator:new k.EnumValidator([0,1]),datasetBindingValidator:new s.DatasetBindingValidator,datasetCreateConfigValidator:new x.DatasetCreateConfigValidator,datasourceConnectionConfigValidator:new x.DatasourceConnectionConfigValidator,displayStateModeValidator:new k.EnumValidator([0,1]),displayStateValidator:new p.DisplayStateValidator,exportDataRequestValidator:new d.ExportDataRequestValidator,extensionArrayValidator:new k.ArrayValidator([new u.ExtensionValidator]),extensionsValidator:new S.AnyOfValidator([new k.ArrayValidator([new u.ExtensionValidator]),new u.ExtensionsValidator]),extensionPointsValidator:new u.ExtensionPointsValidator,extensionValidator:new u.ExtensionValidator,fieldForbiddenValidator:new T.FieldForbiddenValidator,fieldRequiredValidator:new E.FieldRequiredValidator,fieldsPaneValidator:new h.FieldsPaneValidator,filterColumnTargetValidator:new c.FilterColumnTargetValidator,filterDisplaySettingsValidator:new c.FilterDisplaySettingsValidator,filterConditionsValidator:new k.ArrayValidator([new c.ConditionItemValidator]),filterHierarchyTargetValidator:new c.FilterHierarchyTargetValidator,filterMeasureTargetValidator:new c.FilterMeasureTargetValidator,filterTargetValidator:new S.AnyOfValidator([new c.FilterColumnTargetValidator,new c.FilterHierarchyTargetValidator,new c.FilterMeasureTargetValidator,new k.ArrayValidator([new S.AnyOfValidator([new c.FilterColumnTargetValidator,new c.FilterHierarchyTargetValidator,new c.FilterMeasureTargetValidator,new c.FilterKeyColumnsTargetValidator,new c.FilterKeyHierarchyTargetValidator])])]),filterValidator:new c.FilterValidator,filterTypeValidator:new k.EnumValidator([0,1,2,3,4,5,6,7,9]),filtersArrayValidator:new k.ArrayValidator([new c.FilterValidator]),filtersOperationsUpdateValidator:new k.EnumValidator([1,2,3]),filtersOperationsRemoveAllValidator:new k.EnumValidator([0]),filtersPaneValidator:new h.FiltersPaneValidator,hyperlinkClickBehaviorValidator:new k.EnumValidator([0,1,2]),includeExcludeFilterValidator:new c.IncludeExcludeFilterValidator,includeExludeFilterTypeValidator:new k.EnumValidator([3]),hierarchyFilterTypeValidator:new k.EnumValidator([9]),hierarchyFilterValuesValidator:new k.ArrayValidator([new c.HierarchyFilterNodeValidator]),layoutTypeValidator:new k.EnumValidator([0,1,2,3]),loadQnaValidator:new v.LoadQnaValidator,menuExtensionValidator:new S.AnyOfValidator([new u.FlatMenuExtensionValidator,new u.GroupedMenuExtensionValidator]),menuGroupExtensionArrayValidator:new k.ArrayValidator([new u.MenuGroupExtensionValidator]),menuGroupExtensionValidator:new u.MenuGroupExtensionValidator,menuLocationValidator:new k.EnumValidator([0,1]),notSupportedFilterTypeValidator:new k.EnumValidator([2]),notSupportedFilterValidator:new c.NotSupportedFilterValidator,numberArrayValidator:new k.NumberArrayValidator,numberValidator:new k.NumberValidator,onLoadFiltersBaseValidator:new S.AnyOfValidator([new c.OnLoadFiltersBaseValidator,new c.OnLoadFiltersBaseRemoveOperationValidator]),pageLayoutValidator:new C.MapValidator([new k.StringValidator],[new p.VisualLayoutValidator]),pageNavigationPaneValidator:new h.PageNavigationPaneValidator,pageNavigationPositionValidator:new k.EnumValidator([0,1]),pageSizeTypeValidator:new k.EnumValidator([0,1,2,3,4,5]),pageSizeValidator:new f.PageSizeValidator,pageValidator:new f.PageValidator,pageViewFieldValidator:new f.PageViewFieldValidator,pagesLayoutValidator:new C.MapValidator([new k.StringValidator],[new p.PageLayoutValidator]),paginatedReportCommandsValidator:new o.PaginatedReportCommandsValidator,paginatedReportLoadValidator:new g.PaginatedReportLoadValidator,paginatedReportsettingsValidator:new w.PaginatedReportSettingsValidator,parameterValuesArrayValidator:new k.ArrayValidator([new g.ReportParameterFieldsValidator]),parametersPanelValidator:new F.ParametersPanelValidator,permissionsValidator:new k.EnumValidator([0,1,2,4,7]),playBookmarkRequestValidator:new a.PlayBookmarkRequestValidator,printSettingsValidator:new j.PrintSettingsValidator,qnaInterpretInputDataValidator:new v.QnaInterpretInputDataValidator,qnaPanesValidator:new h.QnaPanesValidator,qnaSettingValidator:new v.QnaSettingsValidator,quickCreateValidator:new R.QuickCreateValidator,rawDataValidator:new k.ArrayValidator([new k.ArrayValidator([new k.StringValidator])]),relativeDateFilterOperatorValidator:new k.EnumValidator([0,1,2]),relativeDateFilterTimeUnitTypeValidator:new k.EnumValidator([0,1,2,3,4,5,6]),relativeDateFilterTypeValidator:new k.EnumValidator([4]),relativeDateFilterValidator:new c.RelativeDateFilterValidator,relativeDateTimeFilterTypeValidator:new k.EnumValidator([4,7]),relativeDateTimeFilterUnitTypeValidator:new k.EnumValidator([0,1,2,3,4,5,6,7,8]),relativeTimeFilterTimeUnitTypeValidator:new k.EnumValidator([7,8]),relativeTimeFilterTypeValidator:new k.EnumValidator([7]),relativeTimeFilterValidator:new c.RelativeTimeFilterValidator,reportBarsValidator:new i.ReportBarsValidator,reportCreateValidator:new y.ReportCreateValidator,reportLoadFiltersValidator:new S.AnyOfValidator([new k.ArrayValidator([new c.FilterValidator]),new c.OnLoadFiltersValidator]),reportLoadValidator:new m.ReportLoadValidator,reportPanesValidator:new h.ReportPanesValidator,saveAsParametersValidator:new V.SaveAsParametersValidator,selectionPaneValidator:new h.SelectionPaneValidator,settingsValidator:new w.SettingsValidator,singleCommandSettingsValidator:new o.SingleCommandSettingsValidator,slicerSelectorValidator:new S.AnyOfValidator([new b.VisualSelectorValidator,new b.SlicerTargetSelectorValidator]),slicerStateValidator:new _.SlicerStateValidator,slicerTargetValidator:new S.AnyOfValidator([new c.FilterColumnTargetValidator,new c.FilterHierarchyTargetValidator,new c.FilterMeasureTargetValidator,new c.FilterKeyColumnsTargetValidator,new c.FilterKeyHierarchyTargetValidator]),slicerValidator:new _.SlicerValidator,stringArrayValidator:new k.StringArrayValidator,stringValidator:new k.StringValidator,syncSlicersPaneValidator:new h.SyncSlicersPaneValidator,tableDataArrayValidator:new k.ArrayValidator([new x.TableDataValidator]),tableSchemaListValidator:new k.ArrayValidator([new x.TableSchemaValidator]),tileLoadValidator:new O.TileLoadValidator,tokenTypeValidator:new k.EnumValidator([0,1]),topNFilterTypeValidator:new k.EnumValidator([5]),topNFilterValidator:new c.TopNFilterValidator,updateFiltersRequestValidator:new S.AnyOfValidator([new c.UpdateFiltersRequestValidator,new c.RemoveFiltersRequestValidator]),viewModeValidator:new k.EnumValidator([0,1]),visualCommandSelectorValidator:new S.AnyOfValidator([new b.VisualSelectorValidator,new b.VisualTypeSelectorValidator]),visualHeaderSelectorValidator:new S.AnyOfValidator([new b.VisualSelectorValidator,new b.VisualTypeSelectorValidator]),visualHeaderSettingsValidator:new P.VisualHeaderSettingsValidator,visualHeaderValidator:new P.VisualHeaderValidator,visualHeadersValidator:new k.ArrayValidator([new P.VisualHeaderValidator]),visualizationsPaneValidator:new h.VisualizationsPaneValidator,visualLayoutValidator:new p.VisualLayoutValidator,visualSelectorValidator:new b.VisualSelectorValidator,visualSettingsValidator:new P.VisualSettingsValidator,visualTypeSelectorValidator:new b.VisualTypeSelectorValidator,zoomLevelValidator:new k.RangeValidator(.25,4)}},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.StatusBarValidator=e.ActionBarValidator=e.ReportBarsValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"actionBar",validators:[l.Validators.actionBarValidator]},{field:"statusBar",validators:[l.Validators.statusBarValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ReportBarsValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ActionBarValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.StatusBarValidator=u},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MultipleFieldsValidator=void 0;var r=function(){function t(t){this.fieldValidatorsPairs=t}return t.prototype.validate=function(t,e,r){if(!this.fieldValidatorsPairs)return null;for(var i=e?e+"."+r:r,a=0,o=this.fieldValidatorsPairs;a<o.length;a++)for(var n=o[a],l=0,s=n.validators;l<s.length;l++){var d=s[l].validate(t[n.field],i,n.field);if(d)return d}return null},t}();e.MultipleFieldsValidator=r},function(t,e){var r,i=this&&this.__extends||(r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},r(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)});Object.defineProperty(e,"__esModule",{value:!0}),e.RangeValidator=e.NumberArrayValidator=e.BooleanArrayValidator=e.StringArrayValidator=e.EnumValidator=e.SchemaValidator=e.ValueValidator=e.NumberValidator=e.BooleanValidator=e.StringValidator=e.TypeValidator=e.ArrayValidator=e.ObjectValidator=void 0;var a=function(){function t(){}return t.prototype.validate=function(t,e,r){return null==t?null:"object"!=typeof t||Array.isArray(t)?[{message:void 0!==r?r+" must be an object":"input must be an object",path:e,keyword:"type"}]:null},t}();e.ObjectValidator=a;var o=function(){function t(t){this.itemValidators=t}return t.prototype.validate=function(t,e,r){if(null==t)return null;if(!Array.isArray(t))return[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"type"}];for(var i=0;i<t.length;i++)for(var a=(e?e+".":"")+r+"."+i.toString(),o=0,n=this.itemValidators;o<n.length;o++)if(n[o].validate(t[i],a,r))return[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"type"}];return null},t}();e.ArrayValidator=o;var n=function(){function t(t){this.expectedType=t}return t.prototype.validate=function(t,e,r){return null==t?null:typeof t!==this.expectedType?[{message:r+" must be a "+this.expectedType,path:(e?e+".":"")+r,keyword:"type"}]:null},t}();e.TypeValidator=n;var l=function(t){function e(){return t.call(this,"string")||this}return i(e,t),e}(n);e.StringValidator=l;var s=function(t){function e(){return t.call(this,"boolean")||this}return i(e,t),e}(n);e.BooleanValidator=s;var d=function(t){function e(){return t.call(this,"number")||this}return i(e,t),e}(n);e.NumberValidator=d;var u=function(){function t(t){this.possibleValues=t}return t.prototype.validate=function(t,e,r){return null==t?null:this.possibleValues.indexOf(t)<0?[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"invalid"}]:null},t}();e.ValueValidator=u;var c=function(t){function e(e){var r=t.call(this,[e])||this;return r.schemaValue=e,r}return i(e,t),e.prototype.validate=function(e,r,i){return t.prototype.validate.call(this,e,r,i)},e}(u);e.SchemaValidator=c;var p=function(t){function e(e){var r=t.call(this)||this;return r.possibleValues=e,r}return i(e,t),e.prototype.validate=function(e,r,i){return null==e?null:t.prototype.validate.call(this,e,r,i)||new u(this.possibleValues).validate(e,r,i)},e}(d);e.EnumValidator=p;var f=function(t){function e(){return t.call(this,[new l])||this}return i(e,t),e.prototype.validate=function(e,r,i){return t.prototype.validate.call(this,e,r,i)?[{message:i+" must be an array of strings",path:(r?r+".":"")+i,keyword:"type"}]:null},e}(o);e.StringArrayValidator=f;var h=function(t){function e(){return t.call(this,[new s])||this}return i(e,t),e.prototype.validate=function(e,r,i){return t.prototype.validate.call(this,e,r,i)?[{message:i+" must be an array of booleans",path:(r?r+".":"")+i,keyword:"type"}]:null},e}(o);e.BooleanArrayValidator=h;var v=function(t){function e(){return t.call(this,[new d])||this}return i(e,t),e.prototype.validate=function(e,r,i){return t.prototype.validate.call(this,e,r,i)?[{message:i+" must be an array of numbers",path:(r?r+".":"")+i,keyword:"type"}]:null},e}(o);e.NumberArrayValidator=v;var y=function(t){function e(e,r){var i=t.call(this)||this;return i.minValue=e,i.maxValue=r,i}return i(e,t),e.prototype.validate=function(e,r,i){return null==e?null:t.prototype.validate.call(this,e,r,i)||(e>this.maxValue||e<this.minValue?[{message:i+" must be a number between "+this.minValue.toString()+" and "+this.maxValue.toString(),path:(r?r+".":"")+i,keyword:"range"}]:null)},e}(d);e.RangeValidator=y},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CaptureBookmarkRequestValidator=e.CaptureBookmarkOptionsValidator=e.ApplyBookmarkStateRequestValidator=e.ApplyBookmarkByNameRequestValidator=e.AddBookmarkRequestValidator=e.PlayBookmarkRequestValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var s=[{field:"playMode",validators:[l.Validators.fieldRequiredValidator,new n.EnumValidator([0,1])]}];return new o.MultipleFieldsValidator(s).validate(e,r,i)},e}(n.ObjectValidator);e.PlayBookmarkRequestValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"state",validators:[l.Validators.stringValidator]},{field:"displayName",validators:[l.Validators.stringValidator]},{field:"apply",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.AddBookmarkRequestValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ApplyBookmarkByNameRequestValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"state",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ApplyBookmarkStateRequestValidator=c;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"personalizeVisuals",validators:[l.Validators.booleanValidator]},{field:"allPages",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.CaptureBookmarkOptionsValidator=p;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"options",validators:[l.Validators.fieldRequiredValidator,l.Validators.captureBookmarkOptionsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.CaptureBookmarkRequestValidator=f},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.PaginatedReportCommandsValidator=e.SingleCommandSettingsValidator=e.CommandsSettingsValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"copy",validators:[l.Validators.singleCommandSettingsValidator]},{field:"drill",validators:[l.Validators.singleCommandSettingsValidator]},{field:"drillthrough",validators:[l.Validators.singleCommandSettingsValidator]},{field:"expandCollapse",validators:[l.Validators.singleCommandSettingsValidator]},{field:"exportData",validators:[l.Validators.singleCommandSettingsValidator]},{field:"includeExclude",validators:[l.Validators.singleCommandSettingsValidator]},{field:"removeVisual",validators:[l.Validators.singleCommandSettingsValidator]},{field:"search",validators:[l.Validators.singleCommandSettingsValidator]},{field:"seeData",validators:[l.Validators.singleCommandSettingsValidator]},{field:"sort",validators:[l.Validators.singleCommandSettingsValidator]},{field:"spotlight",validators:[l.Validators.singleCommandSettingsValidator]},{field:"insightsAnalysis",validators:[l.Validators.singleCommandSettingsValidator]},{field:"addComment",validators:[l.Validators.singleCommandSettingsValidator]},{field:"groupVisualContainers",validators:[l.Validators.singleCommandSettingsValidator]},{field:"summarize",validators:[l.Validators.singleCommandSettingsValidator]},{field:"clearSelection",validators:[l.Validators.singleCommandSettingsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.CommandsSettingsValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"displayOption",validators:[l.Validators.fieldRequiredValidator,l.Validators.commandDisplayOptionValidator]},{field:"selector",validators:[l.Validators.visualCommandSelectorValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SingleCommandSettingsValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"parameterPanel",validators:[l.Validators.parametersPanelValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.PaginatedReportCommandsValidator=u},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CustomThemeValidator=void 0;var o=r(3),n=r(4),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var l=[{field:"themeJson",validators:[new n.ObjectValidator]}];return new o.MultipleFieldsValidator(l).validate(e,r,i)},e}(n.ObjectValidator);e.CustomThemeValidator=l},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.DashboardLoadValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"accessToken",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"id",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"groupId",validators:[l.Validators.stringValidator]},{field:"pageView",validators:[l.Validators.pageViewFieldValidator]},{field:"tokenType",validators:[l.Validators.tokenTypeValidator]},{field:"embedUrl",validators:[l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.DashboardLoadValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.DatasetBindingValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"datasetId",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.DatasetBindingValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ExportDataRequestValidator=void 0;var o=r(3),n=r(4),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var l=[{field:"rows",validators:[new n.NumberValidator]},{field:"exportDataType",validators:[new n.EnumValidator([0,1])]}];return new o.MultipleFieldsValidator(l).validate(e,r,i)},e}(n.ObjectValidator);e.ExportDataRequestValidator=l},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ExtensionsValidator=e.MenuGroupExtensionValidator=e.ExtensionValidator=e.CommandExtensionValidator=e.ExtensionItemValidator=e.ExtensionPointsValidator=e.GroupedMenuExtensionValidator=e.FlatMenuExtensionValidator=e.MenuExtensionBaseValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"title",validators:[l.Validators.stringValidator]},{field:"icon",validators:[l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.MenuExtensionBaseValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"menuLocation",validators:[l.Validators.menuLocationValidator]},{field:"groupName",validators:[l.Validators.fieldForbiddenValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(s);e.FlatMenuExtensionValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"groupName",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"menuLocation",validators:[l.Validators.fieldForbiddenValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(s);e.GroupedMenuExtensionValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visualContextMenu",validators:[l.Validators.menuExtensionValidator]},{field:"visualOptionsMenu",validators:[l.Validators.menuExtensionValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ExtensionPointsValidator=c;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"extend",validators:[l.Validators.fieldRequiredValidator,l.Validators.extensionPointsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ExtensionItemValidator=p;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"title",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"icon",validators:[l.Validators.stringValidator]},{field:"selector",validators:[l.Validators.commandExtensionSelectorValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(p);e.CommandExtensionValidator=f;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"command",validators:[l.Validators.commandExtensionValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ExtensionValidator=h;var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"title",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"menuLocation",validators:[l.Validators.menuLocationValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.MenuGroupExtensionValidator=v;var y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"commands",validators:[l.Validators.fieldRequiredValidator,l.Validators.commandExtensionArrayValidator]},{field:"groups",validators:[l.Validators.menuGroupExtensionArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ExtensionsValidator=y},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.OnLoadFiltersValidator=e.OnLoadFiltersBaseRemoveOperationValidator=e.OnLoadFiltersBaseValidator=e.ConditionItemValidator=e.RemoveFiltersRequestValidator=e.UpdateFiltersRequestValidator=e.FilterValidator=e.HierarchyFilterNodeValidator=e.HierarchyFilterValidator=e.IncludeExcludeFilterValidator=e.NotSupportedFilterValidator=e.TopNFilterValidator=e.RelativeTimeFilterValidator=e.RelativeDateFilterValidator=e.RelativeDateTimeFilterValidator=e.AdvancedFilterValidator=e.BasicFilterValidator=e.FilterValidatorBase=e.FilterDisplaySettingsValidator=e.FilterMeasureTargetValidator=e.FilterKeyHierarchyTargetValidator=e.FilterHierarchyTargetValidator=e.FilterKeyColumnsTargetValidator=e.FilterColumnTargetValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"table",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"column",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.FilterColumnTargetValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"keys",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(s);e.FilterKeyColumnsTargetValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"table",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"hierarchy",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"hierarchyLevel",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.FilterHierarchyTargetValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"keys",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(u);e.FilterKeyHierarchyTargetValidator=c;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"table",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"measure",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.FilterMeasureTargetValidator=p;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"isLockedInViewMode",validators:[l.Validators.booleanValidator]},{field:"isHiddenInViewMode",validators:[l.Validators.booleanValidator]},{field:"displayName",validators:[l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.FilterDisplaySettingsValidator=f;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"target",validators:[l.Validators.fieldRequiredValidator,l.Validators.filterTargetValidator]},{field:"$schema",validators:[l.Validators.stringValidator]},{field:"filterType",validators:[l.Validators.filterTypeValidator]},{field:"displaySettings",validators:[l.Validators.filterDisplaySettingsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.FilterValidatorBase=h;var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"operator",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"values",validators:[l.Validators.fieldRequiredValidator,l.Validators.anyArrayValidator]},{field:"filterType",validators:[l.Validators.basicFilterTypeValidator]},{field:"requireSingleSelection",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(h);e.BasicFilterValidator=v;var y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"logicalOperator",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"conditions",validators:[l.Validators.filterConditionsValidator]},{field:"filterType",validators:[l.Validators.advancedFilterTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(h);e.AdvancedFilterValidator=y;var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"operator",validators:[l.Validators.fieldRequiredValidator,l.Validators.relativeDateFilterOperatorValidator]},{field:"timeUnitsCount",validators:[l.Validators.numberValidator]},{field:"timeUnitType",validators:[l.Validators.fieldRequiredValidator,l.Validators.relativeDateTimeFilterUnitTypeValidator]},{field:"filterType",validators:[l.Validators.relativeDateTimeFilterTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(h);e.RelativeDateTimeFilterValidator=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"includeToday",validators:[l.Validators.fieldRequiredValidator,l.Validators.booleanValidator]},{field:"timeUnitType",validators:[l.Validators.fieldRequiredValidator,l.Validators.relativeDateFilterTimeUnitTypeValidator]},{field:"filterType",validators:[l.Validators.relativeDateFilterTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(m);e.RelativeDateFilterValidator=g;var V=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"timeUnitType",validators:[l.Validators.fieldRequiredValidator,l.Validators.relativeTimeFilterTimeUnitTypeValidator]},{field:"filterType",validators:[l.Validators.relativeTimeFilterTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(m);e.RelativeTimeFilterValidator=V;var b=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"operator",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"itemCount",validators:[l.Validators.fieldRequiredValidator,l.Validators.numberValidator]},{field:"filterType",validators:[l.Validators.topNFilterTypeValidator]},{field:"orderBy",validators:[l.Validators.fieldRequiredValidator,l.Validators.filterTargetValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(h);e.TopNFilterValidator=b;var w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"message",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"notSupportedTypeName",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"filterType",validators:[l.Validators.notSupportedFilterTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(h);e.NotSupportedFilterValidator=w;var _=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"isExclude",validators:[l.Validators.fieldRequiredValidator,l.Validators.booleanValidator]},{field:"values",validators:[l.Validators.fieldRequiredValidator,l.Validators.anyArrayValidator]},{field:"filterType",validators:[l.Validators.includeExludeFilterTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(h);e.IncludeExcludeFilterValidator=_;var O=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"hierarchyData",validators:[l.Validators.fieldRequiredValidator,l.Validators.hierarchyFilterValuesValidator]},{field:"filterType",validators:[l.Validators.hierarchyFilterTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(h);e.HierarchyFilterValidator=O;var P=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"value",validators:[l.Validators.anyValueValidator]},{field:"keyValues",validators:[l.Validators.anyArrayValidator]},{field:"children",validators:[l.Validators.hierarchyFilterValuesValidator]},{field:"operator",validators:[l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.HierarchyFilterNodeValidator=P;var S=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){return null==e?null:t.prototype.validate.call(this,e,r,i)||l.Validators.anyFilterValidator.validate(e,r,i)},e}(n.ObjectValidator);e.FilterValidator=S;var T=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"filtersOperation",validators:[l.Validators.fieldRequiredValidator,l.Validators.filtersOperationsUpdateValidator]},{field:"filters",validators:[l.Validators.fieldRequiredValidator,l.Validators.filtersArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.UpdateFiltersRequestValidator=T;var E=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"filtersOperation",validators:[l.Validators.fieldRequiredValidator,l.Validators.filtersOperationsRemoveAllValidator]},{field:"filters",validators:[l.Validators.fieldForbiddenValidator,l.Validators.filtersArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.RemoveFiltersRequestValidator=E;var C=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"value",validators:[l.Validators.anyValueValidator]},{field:"operator",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ConditionItemValidator=C;var k=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"operation",validators:[l.Validators.fieldRequiredValidator,l.Validators.filtersOperationsUpdateValidator]},{field:"filters",validators:[l.Validators.fieldRequiredValidator,l.Validators.filtersArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.OnLoadFiltersBaseValidator=k;var F=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"operation",validators:[l.Validators.fieldRequiredValidator,l.Validators.filtersOperationsRemoveAllValidator]},{field:"filters",validators:[l.Validators.fieldForbiddenValidator,l.Validators.filtersArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.OnLoadFiltersBaseRemoveOperationValidator=F;var x=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"allPages",validators:[l.Validators.onLoadFiltersBaseValidator]},{field:"currentPage",validators:[l.Validators.onLoadFiltersBaseValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.OnLoadFiltersValidator=x},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.PageLayoutValidator=e.DisplayStateValidator=e.VisualLayoutValidator=e.CustomLayoutValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"pageSize",validators:[l.Validators.pageSizeValidator]},{field:"displayOption",validators:[l.Validators.customLayoutDisplayOptionValidator]},{field:"pagesLayout",validators:[l.Validators.pagesLayoutValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.CustomLayoutValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"x",validators:[l.Validators.numberValidator]},{field:"y",validators:[l.Validators.numberValidator]},{field:"z",validators:[l.Validators.numberValidator]},{field:"width",validators:[l.Validators.numberValidator]},{field:"height",validators:[l.Validators.numberValidator]},{field:"displayState",validators:[l.Validators.displayStateValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.VisualLayoutValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"mode",validators:[l.Validators.displayStateModeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.DisplayStateValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visualsLayout",validators:[l.Validators.fieldRequiredValidator,l.Validators.pageLayoutValidator]},{field:"defaultLayout",validators:[l.Validators.visualLayoutValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.PageLayoutValidator=c},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.PageViewFieldValidator=e.PageValidator=e.CustomPageSizeValidator=e.PageSizeValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"type",validators:[l.Validators.fieldRequiredValidator,l.Validators.pageSizeTypeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.PageSizeValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"width",validators:[l.Validators.numberValidator]},{field:"height",validators:[l.Validators.numberValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(s);e.CustomPageSizeValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.PageValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){return null==e?null:t.prototype.validate.call(this,e,r,i)||(["actualSize","fitToWidth","oneColumn"].indexOf(e)<0?[{message:'pageView must be a string with one of the following values: "actualSize", "fitToWidth", "oneColumn"'}]:null)},e}(n.StringValidator);e.PageViewFieldValidator=c},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.VisualizationsPaneValidator=e.SyncSlicersPaneValidator=e.SelectionPaneValidator=e.PageNavigationPaneValidator=e.FiltersPaneValidator=e.FieldsPaneValidator=e.BookmarksPaneValidator=e.QnaPanesValidator=e.ReportPanesValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"bookmarks",validators:[l.Validators.bookmarksPaneValidator]},{field:"fields",validators:[l.Validators.fieldsPaneValidator]},{field:"filters",validators:[l.Validators.filtersPaneValidator]},{field:"pageNavigation",validators:[l.Validators.pageNavigationPaneValidator]},{field:"selection",validators:[l.Validators.selectionPaneValidator]},{field:"syncSlicers",validators:[l.Validators.syncSlicersPaneValidator]},{field:"visualizations",validators:[l.Validators.visualizationsPaneValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ReportPanesValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"filters",validators:[l.Validators.filtersPaneValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.QnaPanesValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.BookmarksPaneValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"expanded",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.FieldsPaneValidator=c;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]},{field:"expanded",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.FiltersPaneValidator=p;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]},{field:"position",validators:[l.Validators.pageNavigationPositionValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.PageNavigationPaneValidator=f;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SelectionPaneValidator=h;var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SyncSlicersPaneValidator=v;var y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"expanded",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.VisualizationsPaneValidator=y},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.QnaInterpretInputDataValidator=e.QnaSettingsValidator=e.LoadQnaValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"accessToken",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"datasetIds",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringArrayValidator]},{field:"question",validators:[l.Validators.stringValidator]},{field:"viewMode",validators:[l.Validators.viewModeValidator]},{field:"settings",validators:[l.Validators.qnaSettingValidator]},{field:"tokenType",validators:[l.Validators.tokenTypeValidator]},{field:"groupId",validators:[l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.LoadQnaValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"filterPaneEnabled",validators:[l.Validators.booleanValidator]},{field:"hideErrors",validators:[l.Validators.booleanValidator]},{field:"panes",validators:[l.Validators.qnaPanesValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.QnaSettingsValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"datasetIds",validators:[l.Validators.stringArrayValidator]},{field:"question",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.QnaInterpretInputDataValidator=u},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ReportCreateValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"accessToken",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"datasetId",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"groupId",validators:[l.Validators.stringValidator]},{field:"tokenType",validators:[l.Validators.tokenTypeValidator]},{field:"theme",validators:[l.Validators.customThemeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ReportCreateValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ReportLoadValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"accessToken",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"id",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"groupId",validators:[l.Validators.stringValidator]},{field:"settings",validators:[l.Validators.settingsValidator]},{field:"pageName",validators:[l.Validators.stringValidator]},{field:"filters",validators:[l.Validators.reportLoadFiltersValidator]},{field:"permissions",validators:[l.Validators.permissionsValidator]},{field:"viewMode",validators:[l.Validators.viewModeValidator]},{field:"tokenType",validators:[l.Validators.tokenTypeValidator]},{field:"bookmark",validators:[l.Validators.applyBookmarkValidator]},{field:"theme",validators:[l.Validators.customThemeValidator]},{field:"embedUrl",validators:[l.Validators.stringValidator]},{field:"datasetBinding",validators:[l.Validators.datasetBindingValidator]},{field:"contrastMode",validators:[l.Validators.contrastModeValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ReportLoadValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ReportParameterFieldsValidator=e.PaginatedReportLoadValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"accessToken",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"id",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"groupId",validators:[l.Validators.stringValidator]},{field:"settings",validators:[l.Validators.paginatedReportsettingsValidator]},{field:"tokenType",validators:[l.Validators.tokenTypeValidator]},{field:"embedUrl",validators:[l.Validators.stringValidator]},{field:"type",validators:[l.Validators.stringValidator]},{field:"parameterValues",validators:[l.Validators.parameterValuesArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.PaginatedReportLoadValidator=s;var d=function(){function t(){}return t.prototype.validate=function(t,e,r){if(null==t)return null;var i=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"value",validators:[l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(i).validate(t,e,r)},t}();e.ReportParameterFieldsValidator=d},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SaveAsParametersValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SaveAsParametersValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SlicerTargetSelectorValidator=e.VisualTypeSelectorValidator=e.VisualSelectorValidator=void 0;var o=r(3),n=r(4),l=r(4),s=r(1),d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"$schema",validators:[s.Validators.stringValidator,new l.SchemaValidator("http://powerbi.com/product/schema#visualSelector")]},{field:"visualName",validators:[s.Validators.fieldRequiredValidator,s.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.VisualSelectorValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"$schema",validators:[s.Validators.fieldRequiredValidator,s.Validators.stringValidator,new l.SchemaValidator("http://powerbi.com/product/schema#visualTypeSelector")]},{field:"visualType",validators:[s.Validators.fieldRequiredValidator,s.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.VisualTypeSelectorValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"$schema",validators:[s.Validators.fieldRequiredValidator,s.Validators.stringValidator,new l.SchemaValidator("http://powerbi.com/product/schema#slicerTargetSelector")]},{field:"target",validators:[s.Validators.fieldRequiredValidator,s.Validators.slicerTargetValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SlicerTargetSelectorValidator=c},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.PaginatedReportSettingsValidator=e.SettingsValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"filterPaneEnabled",validators:[l.Validators.booleanValidator]},{field:"navContentPaneEnabled",validators:[l.Validators.booleanValidator]},{field:"bookmarksPaneEnabled",validators:[l.Validators.booleanValidator]},{field:"useCustomSaveAsDialog",validators:[l.Validators.booleanValidator]},{field:"extensions",validators:[l.Validators.extensionsValidator]},{field:"layoutType",validators:[l.Validators.layoutTypeValidator]},{field:"customLayout",validators:[l.Validators.customLayoutValidator]},{field:"background",validators:[l.Validators.backgroundValidator]},{field:"visualSettings",validators:[l.Validators.visualSettingsValidator]},{field:"hideErrors",validators:[l.Validators.booleanValidator]},{field:"commands",validators:[l.Validators.commandsSettingsArrayValidator]},{field:"hyperlinkClickBehavior",validators:[l.Validators.hyperlinkClickBehaviorValidator]},{field:"bars",validators:[l.Validators.reportBarsValidator]},{field:"panes",validators:[l.Validators.reportPanesValidator]},{field:"personalBookmarksEnabled",validators:[l.Validators.booleanValidator]},{field:"persistentFiltersEnabled",validators:[l.Validators.booleanValidator]},{field:"visualRenderedEvents",validators:[l.Validators.booleanValidator]},{field:"authoringHintsEnabled",validators:[l.Validators.booleanValidator]},{field:"printSettings",validators:[l.Validators.printSettingsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SettingsValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"commands",validators:[l.Validators.paginatedReportCommandsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.PaginatedReportSettingsValidator=d},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SlicerStateValidator=e.SlicerValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"selector",validators:[l.Validators.fieldRequiredValidator,l.Validators.slicerSelectorValidator]},{field:"state",validators:[l.Validators.fieldRequiredValidator,l.Validators.slicerStateValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SlicerValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"filters",validators:[l.Validators.filtersArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.SlicerStateValidator=d},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.TileLoadValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"accessToken",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"id",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"dashboardId",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"groupId",validators:[l.Validators.stringValidator]},{field:"pageView",validators:[l.Validators.stringValidator]},{field:"tokenType",validators:[l.Validators.tokenTypeValidator]},{field:"width",validators:[l.Validators.numberValidator]},{field:"height",validators:[l.Validators.numberValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.TileLoadValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.VisualHeaderValidator=e.VisualHeaderSettingsValidator=e.VisualSettingsValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visualHeaders",validators:[l.Validators.visualHeadersValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.VisualSettingsValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"visible",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.VisualHeaderSettingsValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"settings",validators:[l.Validators.fieldRequiredValidator,l.Validators.visualHeaderSettingsValidator]},{field:"selector",validators:[l.Validators.visualHeaderSelectorValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.VisualHeaderValidator=u},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AnyOfValidator=void 0;var r=function(){function t(t){this.validators=t}return t.prototype.validate=function(t,e,r){if(null==t)return null;for(var i=!1,a=0,o=this.validators;a<o.length;a++)if(!o[a].validate(t,e,r)){i=!0;break}return i?null:[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"invalid"}]},t}();e.AnyOfValidator=r},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.FieldForbiddenValidator=void 0;var r=function(){function t(){}return t.prototype.validate=function(t,e,r){return void 0!==t?[{message:r+" is forbidden",path:(e?e+".":"")+r,keyword:"forbidden"}]:null},t}();e.FieldForbiddenValidator=r},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.FieldRequiredValidator=void 0;var r=function(){function t(){}return t.prototype.validate=function(t,e,r){return null==t?[{message:r+" is required",path:(e?e+".":"")+r,keyword:"required"}]:null},t}();e.FieldRequiredValidator=r},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MapValidator=void 0;var o=function(t){function e(e,r){var i=t.call(this)||this;return i.keyValidators=e,i.valueValidators=r,i}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;for(var o in e)if(e.hasOwnProperty(o)){for(var n=(r?r+".":"")+i+"."+o,l=0,s=this.keyValidators;l<s.length;l++)if(a=s[l].validate(o,n,i))return a;for(var d=0,u=this.valueValidators;d<u.length;d++)if(a=u[d].validate(e[o],n,i))return a}return null},e}(r(4).ObjectValidator);e.MapValidator=o},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ParametersPanelValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"expanded",validators:[l.Validators.booleanValidator]},{field:"enabled",validators:[l.Validators.booleanValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ParametersPanelValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.TableDataValidator=e.TableSchemaValidator=e.ColumnSchemaValidator=e.CredentialsValidator=e.DatasourceConnectionConfigValidator=e.DatasetCreateConfigValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"locale",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"mashupDocument",validators:[l.Validators.stringValidator]},{field:"datasourceConnectionConfig",validators:[l.Validators.datasourceConnectionConfigValidator]},{field:"tableSchemaList",validators:[l.Validators.tableSchemaListValidator]},{field:"data",validators:[l.Validators.tableDataArrayValidator]}];return(a=new o.MultipleFieldsValidator(n).validate(e,r,i))||(e.datasourceConnectionConfig&&null==e.mashupDocument?[{message:"mashupDocument cannot be empty when datasourceConnectionConfig is presented"}]:e.data&&null==e.tableSchemaList?[{message:"tableSchemaList cannot be empty when data is provided"}]:null==e.data&&null==e.mashupDocument?[{message:"At least one of data or mashupDocument must be provided"}]:void 0)},e}(n.ObjectValidator);e.DatasetCreateConfigValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"dataCacheMode",validators:[l.Validators.dataCacheModeValidator]},{field:"credentials",validators:[l.Validators.credentialsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.DatasourceConnectionConfigValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"credentialType",validators:[l.Validators.credentialTypeValidator]},{field:"credentialDetails",validators:[l.Validators.credentialDetailsValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.CredentialsValidator=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"displayName",validators:[l.Validators.stringValidator]},{field:"dataType",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.ColumnSchemaValidator=c;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"columns",validators:[l.Validators.fieldRequiredValidator,l.Validators.columnSchemaArrayValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.TableSchemaValidator=p;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"name",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"rows",validators:[l.Validators.fieldRequiredValidator,l.Validators.rawDataValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.TableDataValidator=f},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.QuickCreateValidator=void 0;var o=r(3),n=r(4),l=r(1),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var n=[{field:"accessToken",validators:[l.Validators.fieldRequiredValidator,l.Validators.stringValidator]},{field:"groupId",validators:[l.Validators.stringValidator]},{field:"tokenType",validators:[l.Validators.tokenTypeValidator]},{field:"theme",validators:[l.Validators.customThemeValidator]},{field:"datasetCreateConfig",validators:[l.Validators.fieldRequiredValidator,l.Validators.datasetCreateConfigValidator]}];return new o.MultipleFieldsValidator(n).validate(e,r,i)},e}(n.ObjectValidator);e.QuickCreateValidator=s},function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.PrintSettingsValidator=void 0;var o=r(3),n=r(4),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,i){if(null==e)return null;var a=t.prototype.validate.call(this,e,r,i);if(a)return a;var l=[{field:"browserPrintAdjustmentsMode",validators:[new n.EnumValidator([0,1])]}];return new o.MultipleFieldsValidator(l).validate(e,r,i)},e}(n.ObjectValidator);e.PrintSettingsValidator=l}],e={},r=function r(i){var a=e[i];if(void 0!==a)return a.exports;var o=e[i]={exports:{}};return t[i].call(o.exports,o,o.exports,r),o.exports}(0);return r})(),t.exports=e()},"./node_modules/powerbi-router/dist/router.js":function(t){var e;e=function(){return function(t){var e={};function r(i){if(e[i])return e[i].exports;var a=e[i]={exports:{},id:i,loaded:!1};return t[i].call(a.exports,a,a.exports,r),a.loaded=!0,a.exports}return r.m=t,r.c=e,r.p="",r(0)}([function(t,e,r){"use strict";var i=r(1),a=function(){function t(t){this.handlers=t,this.getRouteRecognizer=new i,this.patchRouteRecognizer=new i,this.postRouteRecognizer=new i,this.putRouteRecognizer=new i,this.deleteRouteRecognizer=new i}return t.prototype.get=function(t,e){return this.registerHandler(this.getRouteRecognizer,"GET",t,e),this},t.prototype.patch=function(t,e){return this.registerHandler(this.patchRouteRecognizer,"PATCH",t,e),this},t.prototype.post=function(t,e){return this.registerHandler(this.postRouteRecognizer,"POST",t,e),this},t.prototype.put=function(t,e){return this.registerHandler(this.putRouteRecognizer,"PUT",t,e),this},t.prototype.delete=function(t,e){return this.registerHandler(this.deleteRouteRecognizer,"DELETE",t,e),this},t.prototype.registerHandler=function(t,e,r,i){t.add([{path:r,handler:function(t){var e=new o;return Promise.resolve(i(t,e)).then((function(t){return e}))}}]);var a={test:function(r){if(r.method!==e)return!1;var i=t.recognize(r.url);if(void 0===i)return!1;var a=i[0];return r.params=a.params,r.queryParams=i.queryParams,r.handler=a.handler,!0},handle:function(t){return t.handler(t)}};this.handlers.addHandler(a)},t}();e.Router=a;var o=function(){function t(){this.statusCode=200,this.headers={},this.body=null}return t.prototype.send=function(t,e){this.statusCode=t,this.body=e},t}();e.Response=o},function(t,e,r){var i;(function(t){(function(){"use strict";function a(t,e,r){this.path=t,this.matcher=e,this.delegate=r}function o(t){this.routes={},this.children={},this.target=t}function n(t,e,r){return function(i,o){var l=t+i;if(!o)return new a(t+i,e,r);o(n(l,e,r))}}function l(t,e,r){for(var i=0,a=0;a<t.length;a++)i+=t[a].path.length;var o={path:e=e.substr(i),handler:r};t.push(o)}function s(t,e,r,i){var a=e.routes;for(var o in a)if(a.hasOwnProperty(o)){var n=t.slice();l(n,o,a[o]),e.children[o]?s(n,e.children[o],r,i):r.call(i,n)}}a.prototype={to:function(t,e){var r=this.delegate;if(r&&r.willAddRoute&&(t=r.willAddRoute(this.matcher.target,t)),this.matcher.add(this.path,t),e){if(0===e.length)throw new Error("You must have an argument in the function passed to `to`");this.matcher.addChild(this.path,t,e,this.delegate)}return this}},o.prototype={add:function(t,e){this.routes[t]=e},addChild:function(t,e,r,i){var a=new o(e);this.children[t]=a;var l=n(t,a,i);i&&i.contextEntered&&i.contextEntered(e,l),r(l)}};var d=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\"].join("|\\")+")","g");function u(t){this.string=t}function c(t){this.name=t}function p(t){this.name=t}function f(){}function h(t,e,r){"/"===t.charAt(0)&&(t=t.substr(1));var i=t.split("/"),a=new Array(i.length);r.val="";for(var o=0;o<i.length;o++){var n,l=i[o];(n=l.match(/^:([^\/]+)$/))?(a[o]=new c(n[1]),e.push(n[1]),r.val+="3"):(n=l.match(/^\*([^\/]+)$/))?(a[o]=new p(n[1]),r.val+="1",e.push(n[1])):""===l?(a[o]=new f,r.val+="2"):(a[o]=new u(l),r.val+="4")}return r.val=+r.val,a}function v(t){this.charSpec=t,this.nextStates=[],this.charSpecs={},this.regex=void 0,this.handlers=void 0,this.specificity=void 0}function y(t,e){for(var r=[],i=0,a=t.length;i<a;i++){var o=t[i];r=r.concat(o.match(e))}return r}u.prototype={eachChar:function(t){for(var e,r=this.string,i=0;i<r.length;i++)e=r.charAt(i),t=t.put({invalidChars:void 0,repeat:!1,validChars:e});return t},regex:function(){return this.string.replace(d,"\\$1")},generate:function(){return this.string}},c.prototype={eachChar:function(t){return t.put({invalidChars:"/",repeat:!0,validChars:void 0})},regex:function(){return"([^/]+)"},generate:function(t){return t[this.name]}},p.prototype={eachChar:function(t){return t.put({invalidChars:"",repeat:!0,validChars:void 0})},regex:function(){return"(.+)"},generate:function(t){return t[this.name]}},f.prototype={eachChar:function(t){return t},regex:function(){return""},generate:function(){return""}},v.prototype={get:function(t){if(this.charSpecs[t.validChars])return this.charSpecs[t.validChars];for(var e=this.nextStates,r=0;r<e.length;r++){var i=e[r],a=i.charSpec.validChars===t.validChars;if(a=a&&i.charSpec.invalidChars===t.invalidChars)return this.charSpecs[t.validChars]=i,i}},put:function(t){var e;return(e=this.get(t))||(e=new v(t),this.nextStates.push(e),t.repeat&&e.nextStates.push(e)),e},match:function(t){for(var e,r,i,a=this.nextStates,o=[],n=0;n<a.length;n++)void 0!==(i=(r=(e=a[n]).charSpec).validChars)?-1!==i.indexOf(t)&&o.push(e):void 0!==(i=r.invalidChars)&&-1===i.indexOf(t)&&o.push(e);return o}};var m=Object.create||function(t){function e(){}return e.prototype=t,new e};function g(t){this.queryParams=t||{}}function V(t){var e;t=t.replace(/\+/gm,"%20");try{e=decodeURIComponent(t)}catch(t){e=""}return e}g.prototype=m({splice:Array.prototype.splice,slice:Array.prototype.slice,push:Array.prototype.push,length:0,queryParams:null});var b=function(){this.rootState=new v,this.names={}};(b.prototype={add:function(t,e){for(var r,i=this.rootState,a="^",o={},n=new Array(t.length),l=[],s=!0,d=0;d<t.length;d++){var u=t[d],c=[],p=h(u.path,c,o);l=l.concat(p);for(var v=0;v<p.length;v++){var y=p[v];y instanceof f||(s=!1,i=i.put({invalidChars:void 0,repeat:!1,validChars:"/"}),a+="/",i=y.eachChar(i),a+=y.regex())}var m={handler:u.handler,names:c};n[d]=m}s&&(i=i.put({invalidChars:void 0,repeat:!1,validChars:"/"}),a+="/"),i.handlers=n,i.regex=new RegExp(a+"$"),i.specificity=o,(r=e&&e.as)&&(this.names[r]={segments:l,handlers:n})},handlersFor:function(t){var e=this.names[t];if(!e)throw new Error("There is no route named "+t);for(var r=new Array(e.handlers.length),i=0;i<e.handlers.length;i++)r[i]=e.handlers[i];return r},hasRoute:function(t){return!!this.names[t]},generate:function(t,e){var r=this.names[t],i="";if(!r)throw new Error("There is no route named "+t);for(var a=r.segments,o=0;o<a.length;o++){var n=a[o];n instanceof f||(i+="/",i+=n.generate(e))}return"/"!==i.charAt(0)&&(i="/"+i),e&&e.queryParams&&(i+=this.generateQueryString(e.queryParams,r.handlers)),i},generateQueryString:function(t,e){var r,i=[],a=[];for(var o in t)t.hasOwnProperty(o)&&a.push(o);a.sort();for(var n=0;n<a.length;n++){var l=t[o=a[n]];if(null!=l){var s=encodeURIComponent(o);if(r=l,"[object Array]"===Object.prototype.toString.call(r))for(var d=0;d<l.length;d++){var u=o+"[]="+encodeURIComponent(l[d]);i.push(u)}else s+="="+encodeURIComponent(l),i.push(s)}}return 0===i.length?"":"?"+i.join("&")},parseQueryString:function(t){for(var e=t.split("&"),r={},i=0;i<e.length;i++){var a,o=e[i].split("="),n=V(o[0]),l=n.length,s=!1;1===o.length?a="true":(l>2&&"[]"===n.slice(l-2)&&(s=!0,r[n=n.slice(0,l-2)]||(r[n]=[])),a=o[1]?V(o[1]):""),s?r[n].push(a):r[n]=a}return r},recognize:function(t){var e,r,i,a=[this.rootState],o={},n=!1;if(-1!==(i=t.indexOf("?"))){var l=t.substr(i+1,t.length);t=t.substr(0,i),o=this.parseQueryString(l)}for("/"!==(t=decodeURI(t)).charAt(0)&&(t="/"+t),(e=t.length)>1&&"/"===t.charAt(e-1)&&(t=t.substr(0,e-1),n=!0),r=0;r<t.length&&(a=y(a,t.charAt(r))).length;r++);var s=[];for(r=0;r<a.length;r++)a[r].handlers&&s.push(a[r]);a=function(t){return t.sort((function(t,e){return e.specificity.val-t.specificity.val}))}(s);var d=s[0];if(d&&d.handlers)return n&&"(.+)$"===d.regex.source.slice(-5)&&(t+="/"),function(t,e,r){var i=t.handlers,a=t.regex,o=e.match(a),n=1,l=new g(r);l.length=i.length;for(var s=0;s<i.length;s++){for(var d=i[s],u=d.names,c={},p=0;p<u.length;p++)c[u[p]]=o[n++];l[s]={handler:d.handler,params:c,isDynamic:!!u.length}}return l}(d,t,o)}}).map=function(t,e){var r=new o;t(n("",r,this.delegate)),s([],r,(function(t){e?e(this,t):this.add(t)}),this)},b.VERSION="0.1.11";var w=b;r(3).amd?void 0===(i=function(){return w}.call(e,r,e,t))||(t.exports=i):void 0!==t&&t.exports?t.exports=w:void 0!==this&&(this.RouteRecognizer=w)}).call(this)}).call(e,r(2)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children=[],t.webpackPolyfill=1),t}},function(t,e){t.exports=function(){throw new Error("define cannot be used indirect")}}])},t.exports=e()},"./src/FilterBuilders/advancedFilterBuilder.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.AdvancedFilterBuilder=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.conditions=[],e}return a(e,t),e.prototype.and=function(){return this.logicalOperator="And",this},e.prototype.or=function(){return this.logicalOperator="Or",this},e.prototype.addCondition=function(t,e){var r={operator:t,value:e};return this.conditions.push(r),this},e.prototype.build=function(){return new o.AdvancedFilter(this.target,this.logicalOperator,this.conditions)},e}(r("./src/FilterBuilders/filterBuilder.ts").FilterBuilder);e.AdvancedFilterBuilder=n},"./src/FilterBuilders/basicFilterBuilder.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BasicFilterBuilder=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.isRequireSingleSelection=!1,e}return a(e,t),e.prototype.in=function(t){return this.operator="In",this.values=t,this},e.prototype.notIn=function(t){return this.operator="NotIn",this.values=t,this},e.prototype.all=function(){return this.operator="All",this.values=[],this},e.prototype.requireSingleSelection=function(t){return void 0===t&&(t=!1),this.isRequireSingleSelection=t,this},e.prototype.build=function(){var t=new o.BasicFilter(this.target,this.operator,this.values);return t.requireSingleSelection=this.isRequireSingleSelection,t},e}(r("./src/FilterBuilders/filterBuilder.ts").FilterBuilder);e.BasicFilterBuilder=n},"./src/FilterBuilders/filterBuilder.ts":(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.FilterBuilder=void 0;var r=function(){function t(){}return t.prototype.withTargetObject=function(t){return this.target=t,this},t.prototype.withColumnTarget=function(t,e){return this.target={table:t,column:e},this},t.prototype.withMeasureTarget=function(t,e){return this.target={table:t,measure:e},this},t.prototype.withHierarchyLevelTarget=function(t,e,r){return this.target={table:t,hierarchy:e,hierarchyLevel:r},this},t.prototype.withColumnAggregation=function(t,e,r){return this.target={table:t,column:e,aggregationFunction:r},this},t.prototype.withHierarchyLevelAggregationTarget=function(t,e,r,i){return this.target={table:t,hierarchy:e,hierarchyLevel:r,aggregationFunction:i},this},t}();e.FilterBuilder=r},"./src/FilterBuilders/index.ts":(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.RelativeTimeFilterBuilder=e.RelativeDateFilterBuilder=e.TopNFilterBuilder=e.AdvancedFilterBuilder=e.BasicFilterBuilder=void 0;var i=r("./src/FilterBuilders/basicFilterBuilder.ts");Object.defineProperty(e,"BasicFilterBuilder",{enumerable:!0,get:function(){return i.BasicFilterBuilder}});var a=r("./src/FilterBuilders/advancedFilterBuilder.ts");Object.defineProperty(e,"AdvancedFilterBuilder",{enumerable:!0,get:function(){return a.AdvancedFilterBuilder}});var o=r("./src/FilterBuilders/topNFilterBuilder.ts");Object.defineProperty(e,"TopNFilterBuilder",{enumerable:!0,get:function(){return o.TopNFilterBuilder}});var n=r("./src/FilterBuilders/relativeDateFilterBuilder.ts");Object.defineProperty(e,"RelativeDateFilterBuilder",{enumerable:!0,get:function(){return n.RelativeDateFilterBuilder}});var l=r("./src/FilterBuilders/relativeTimeFilterBuilder.ts");Object.defineProperty(e,"RelativeTimeFilterBuilder",{enumerable:!0,get:function(){return l.RelativeTimeFilterBuilder}})},"./src/FilterBuilders/relativeDateFilterBuilder.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.RelativeDateFilterBuilder=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.isTodayIncluded=!0,e}return a(e,t),e.prototype.inLast=function(t,e){return this.operator=o.RelativeDateOperators.InLast,this.timeUnitsCount=t,this.timeUnitType=e,this},e.prototype.inThis=function(t,e){return this.operator=o.RelativeDateOperators.InThis,this.timeUnitsCount=t,this.timeUnitType=e,this},e.prototype.inNext=function(t,e){return this.operator=o.RelativeDateOperators.InNext,this.timeUnitsCount=t,this.timeUnitType=e,this},e.prototype.includeToday=function(t){return this.isTodayIncluded=t,this},e.prototype.build=function(){return new o.RelativeDateFilter(this.target,this.operator,this.timeUnitsCount,this.timeUnitType,this.isTodayIncluded)},e}(r("./src/FilterBuilders/filterBuilder.ts").FilterBuilder);e.RelativeDateFilterBuilder=n},"./src/FilterBuilders/relativeTimeFilterBuilder.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.RelativeTimeFilterBuilder=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.inLast=function(t,e){return this.operator=o.RelativeDateOperators.InLast,this.timeUnitsCount=t,this.timeUnitType=e,this},e.prototype.inThis=function(t,e){return this.operator=o.RelativeDateOperators.InThis,this.timeUnitsCount=t,this.timeUnitType=e,this},e.prototype.inNext=function(t,e){return this.operator=o.RelativeDateOperators.InNext,this.timeUnitsCount=t,this.timeUnitType=e,this},e.prototype.build=function(){return new o.RelativeTimeFilter(this.target,this.operator,this.timeUnitsCount,this.timeUnitType)},e}(r("./src/FilterBuilders/filterBuilder.ts").FilterBuilder);e.RelativeTimeFilterBuilder=n},"./src/FilterBuilders/topNFilterBuilder.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.TopNFilterBuilder=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.top=function(t){return this.operator="Top",this.itemCount=t,this},e.prototype.bottom=function(t){return this.operator="Bottom",this.itemCount=t,this},e.prototype.orderByTarget=function(t){return this.orderByTargetValue=t,this},e.prototype.build=function(){return new o.TopNFilter(this.target,this.operator,this.itemCount,this.orderByTargetValue)},e}(r("./src/FilterBuilders/filterBuilder.ts").FilterBuilder);e.TopNFilterBuilder=n},"./src/bookmarksManager.ts":function(t,e,r){var i=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.BookmarksManager=void 0;var o=r("./src/util.ts"),n=r("./src/errors.ts"),l=function(){function t(t,e,r){this.service=t,this.config=e,this.iframe=r}return t.prototype.getBookmarks=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:if((0,o.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(n.APINotSupportedForRDLError)];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.service.hpm.get("/report/bookmarks",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,t.sent().body];case 3:throw t.sent().body;case 4:return[2]}}))}))},t.prototype.apply=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(r){switch(r.label){case 0:if((0,o.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(n.APINotSupportedForRDLError)];e={name:t},r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/bookmarks/applyByName",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent()];case 3:throw r.sent().body;case 4:return[2]}}))}))},t.prototype.play=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(r){switch(r.label){case 0:if((0,o.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(n.APINotSupportedForRDLError)];e={playMode:t},r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/bookmarks/play",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent()];case 3:throw r.sent().body;case 4:return[2]}}))}))},t.prototype.capture=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(r){switch(r.label){case 0:if((0,o.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(n.APINotSupportedForRDLError)];e={options:t||{}},r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/bookmarks/capture",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent().body];case 3:throw r.sent().body;case 4:return[2]}}))}))},t.prototype.applyState=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(r){switch(r.label){case 0:if((0,o.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(n.APINotSupportedForRDLError)];e={state:t},r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/bookmarks/applyState",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent()];case 3:throw r.sent().body;case 4:return[2]}}))}))},t}();e.BookmarksManager=l},"./src/config.ts":(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default={version:"2.22.3",type:"js"}},"./src/create.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},n=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.Create=void 0;var l=r("./node_modules/powerbi-models/dist/models.js"),s=r("./src/embed.ts"),d=r("./src/util.ts"),u=function(t){function e(e,r,i,a,o){return t.call(this,e,r,i,void 0,a,o)||this}return a(e,t),e.prototype.getId=function(){var t=this.createConfig&&this.createConfig.datasetId?this.createConfig.datasetId:e.findIdFromEmbedUrl(this.config.embedUrl);if("string"!=typeof t||0===t.length)throw new Error("Dataset id is required, but it was not found. You must provide an id either as part of embed configuration.");return t},e.prototype.validate=function(t){return(0,l.validateCreateReport)(t)},e.prototype.configChanged=function(t){if(!t){var e=this.config;this.createConfig={accessToken:e.accessToken,datasetId:e.datasetId||this.getId(),groupId:e.groupId,settings:e.settings,tokenType:e.tokenType,theme:e.theme}}},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"reportEmbed"},e.prototype.isSaved=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return[4,d.isSavedInternal(this.service.hpm,this.config.uniqueId,this.iframe.contentWindow)];case 1:return[2,t.sent()]}}))}))},e.findIdFromEmbedUrl=function(t){var e,r=t.match(/datasetId="?([^&]+)"?/);return r&&(e=r[1]),e},e.prototype.create=function(){var t;return o(this,void 0,void 0,(function(){var e,r;return n(this,(function(i){switch(i.label){case 0:if(e=(0,l.validateCreateReport)(this.createConfig))throw e;i.label=1;case 1:return i.trys.push([1,3,,4]),r={uid:this.config.uniqueId,sdkSessionId:this.service.getSdkSessionId()},(null===(t=this.eventHooks)||void 0===t?void 0:t.accessTokenProvider)&&(r.tokenProviderSupplied=!0),[4,this.service.hpm.post("/report/create",this.createConfig,r,this.iframe.contentWindow)];case 2:return[2,i.sent().body];case 3:throw i.sent().body;case 4:return[2]}}))}))},e}(s.Embed);e.Create=u},"./src/dashboard.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.Dashboard=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(t){function e(r,i,a,o,n){var l=t.call(this,r,i,a,void 0,o,n)||this;return l.loadPath="/dashboard/load",l.phasedLoadPath="/dashboard/prepare",Array.prototype.push.apply(l.allowedEvents,e.allowedEvents),l}return a(e,t),e.findIdFromEmbedUrl=function(t){var e,r=t.match(/dashboardId="?([^&]+)"?/);return r&&(e=r[1]),e},e.prototype.getId=function(){var t=this.config,r=t.id||this.element.getAttribute(e.dashboardIdAttribute)||e.findIdFromEmbedUrl(t.embedUrl);if("string"!=typeof r||0===r.length)throw new Error("Dashboard id is required, but it was not found. You must provide an id either as part of embed configuration or as attribute '".concat(e.dashboardIdAttribute,"'."));return r},e.prototype.validate=function(t){var e=t;return(0,o.validateDashboardLoad)(e)||this.validatePageView(e.pageView)},e.prototype.configChanged=function(t){t||(this.config.id=this.getId())},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"dashboardEmbed"},e.prototype.validatePageView=function(t){if(t&&"fitToWidth"!==t&&"oneColumn"!==t&&"actualSize"!==t)return[{message:"pageView must be one of the followings: fitToWidth, oneColumn, actualSize"}]},e.allowedEvents=["tileClicked","error"],e.dashboardIdAttribute="powerbi-dashboard-id",e.typeAttribute="powerbi-type",e.type="Dashboard",e}(r("./src/embed.ts").Embed);e.Dashboard=n},"./src/embed.ts":function(t,e,r){var i=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.Embed=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=r("./src/config.ts"),l=r("./src/errors.ts"),s=r("./src/util.ts"),d=function(){function t(e,r,i,a,o,n){if(this.allowedEvents=[],(0,s.autoAuthInEmbedUrl)(i.embedUrl))throw new Error(l.EmbedUrlNotSupported);Array.prototype.push.apply(this.allowedEvents,t.allowedEvents),this.eventHandlers=[],this.service=e,this.element=r,this.iframe=a,this.iframeLoaded=!1,this.embedtype=i.type.toLowerCase(),this.commands=[],this.groups=[],this.populateConfig(i,n),(0,s.isCreate)(this.embedtype)?this.setIframe(!1,o,n):this.setIframe(!0,o,n)}return t.prototype.create=function(){throw new Error("no create support")},t.prototype.save=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.service.hpm.post("/report/save",null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},t.prototype.saveAs=function(t){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.service.hpm.post("/report/saveAs",t,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,e.sent().body];case 2:throw e.sent().body;case 3:return[2]}}))}))},t.prototype.getCorrelationId=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.service.hpm.get("/getCorrelationId",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},t.prototype.load=function(t){var e;return i(this,void 0,void 0,(function(){var r,i,o;return a(this,(function(a){switch(a.label){case 0:if(!this.config.accessToken)return console.debug("Power BI SDK iframe is loaded but powerbi.embed is not called yet."),[2];if(!this.iframeLoaded)return console.debug("Power BI SDK is trying to post /report/load before iframe is ready."),[2];if(r=t&&"report"===this.config.type?this.phasedLoadPath:this.loadPath,i={uid:this.config.uniqueId,sdkSessionId:this.service.getSdkSessionId(),bootstrapped:this.config.bootstrapped,sdkVersion:n.default.version},(null===(e=this.eventHooks)||void 0===e?void 0:e.accessTokenProvider)&&(i.tokenProviderSupplied=!0),o=new Date,this.lastLoadRequest&&(0,s.getTimeDiffInMilliseconds)(this.lastLoadRequest,o)<100)return console.debug("Power BI SDK sent more than two /report/load requests in the last 100ms interval."),[2];this.lastLoadRequest=o,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,this.service.hpm.post(r,this.config,i,this.iframe.contentWindow)];case 2:return[2,a.sent().body];case 3:throw a.sent().body;case 4:return[2]}}))}))},t.prototype.off=function(t,e){var r=this,i={name:t,type:null,id:null,value:null};e?((0,s.remove)((function(t){return t.test(i)&&t.handle===e}),this.eventHandlers),this.element.removeEventListener(t,e)):this.eventHandlers.filter((function(t){return t.test(i)})).forEach((function(e){(0,s.remove)((function(t){return t===e}),r.eventHandlers),r.element.removeEventListener(t,e.handle)}))},t.prototype.on=function(t,e){if(-1===this.allowedEvents.indexOf(t))throw new Error("eventName must be one of ".concat(this.allowedEvents,". You passed: ").concat(t));this.eventHandlers.push({test:function(e){return e.name===t},handle:e}),this.element.addEventListener(t,e)},t.prototype.reload=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,this.load()];case 1:return[2,t.sent()]}}))}))},t.prototype.setAccessToken=function(e){return i(this,void 0,void 0,(function(){var r,i;return a(this,(function(a){switch(a.label){case 0:if(!e)throw new Error("Access token cannot be empty");r="create"===(r=this.config.type)||"visual"===r||"qna"===r||"quickCreate"===r?"report":r,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,this.service.hpm.post("/"+r+"/token",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return i=a.sent(),this.config.accessToken=e,this.element.setAttribute(t.accessTokenAttribute,e),this.service.accessToken=e,[2,i.body];case 3:throw a.sent().body;case 4:return[2]}}))}))},t.prototype.getAccessToken=function(e){var r=this.config.accessToken||this.element.getAttribute(t.accessTokenAttribute)||e;if(!r)throw new Error("No access token was found for element. You must specify an access token directly on the element using attribute '".concat(t.accessTokenAttribute,"' or specify a global token at: powerbi.accessToken."));return r},t.prototype.populateConfig=function(t,e){var r,i,a,n,l,d,u,c,p,f=this;this.bootstrapConfig?(this.config=(0,s.assign)({},this.bootstrapConfig,t),this.bootstrapConfig=null):this.config=(0,s.assign)({},t),this.config.embedUrl=this.getEmbedUrl(e),this.config.groupId=this.getGroupId(),this.addLocaleToEmbedUrl(t),this.config.uniqueId=this.getUniqueId();var h=null===(i=null===(r=this.config)||void 0===r?void 0:r.settings)||void 0===i?void 0:i.extensions;this.commands=null!==(a=null==h?void 0:h.commands)&&void 0!==a?a:[],this.groups=null!==(n=null==h?void 0:h.groups)&&void 0!==n?n:[],this.initialLayoutType=null!==(u=null===(d=null===(l=this.config)||void 0===l?void 0:l.settings)||void 0===d?void 0:d.layoutType)&&void 0!==u?u:o.LayoutType.Master;var v=null===(p=null===(c=this.config)||void 0===c?void 0:c.settings)||void 0===p?void 0:p.extensions;Array.isArray(v)&&(this.commands=[],v.map((function(t){(null==t?void 0:t.command)&&f.commands.push(t.command)}))),e?(this.bootstrapConfig=this.config,this.bootstrapConfig.bootstrapped=!0):this.config.accessToken=this.getAccessToken(this.service.accessToken),this.eventHooks=this.config.eventHooks,this.validateEventHooks(this.eventHooks),delete this.config.eventHooks,this.configChanged(e)},t.prototype.validateEventHooks=function(t){if(t){for(var e in t)if(t.hasOwnProperty(e)&&"function"!=typeof t[e])throw new Error(e+" must be a function");if(t.applicationContextProvider){if("report"!==this.embedtype.toLowerCase())throw new Error("applicationContextProvider is only supported in report embed");this.config.embedUrl=(0,s.addParamToUrl)(this.config.embedUrl,"registerQueryCallback","true")}if(t.accessTokenProvider&&(-1===["create","quickcreate","report"].indexOf(this.embedtype.toLowerCase())||this.config.tokenType!==o.TokenType.Aad))throw new Error("accessTokenProvider is only supported in report SaaS embed")}},t.prototype.addLocaleToEmbedUrl=function(t){if(t.settings){var e=t.settings.localeSettings;e&&e.language&&(this.config.embedUrl=(0,s.addParamToUrl)(this.config.embedUrl,"language",e.language)),e&&e.formatLocale&&(this.config.embedUrl=(0,s.addParamToUrl)(this.config.embedUrl,"formatLocale",e.formatLocale))}},t.prototype.getEmbedUrl=function(e){var r=this.config.embedUrl||this.element.getAttribute(t.embedUrlAttribute);if(e&&!r&&(r=this.getDefaultEmbedUrl(this.config.hostname)),"string"!=typeof r||0===r.length)throw new Error("Embed Url is required, but it was not found. You must provide an embed url either as part of embed configuration or as attribute '".concat(t.embedUrlAttribute,"'."));return r},t.prototype.getDefaultEmbedUrl=function(e){e||(e=t.defaultEmbedHostName);var r=this.getDefaultEmbedUrlEndpoint();if(0===(e=e.toLowerCase().trim()).indexOf("http://"))throw new Error("HTTP is not allowed. HTTPS is required");return 0===e.indexOf("https://")?"".concat(e,"/").concat(r):"https://".concat(e,"/").concat(r)},t.prototype.getUniqueId=function(){return this.config.uniqueId||this.element.getAttribute(t.nameAttribute)||(0,s.createRandomString)()},t.prototype.getGroupId=function(){return this.config.groupId||t.findGroupIdFromEmbedUrl(this.config.embedUrl)},t.prototype.fullscreen=function(){(this.iframe.requestFullscreen||this.iframe.msRequestFullscreen||this.iframe.mozRequestFullScreen||this.iframe.webkitRequestFullscreen).call(this.iframe)},t.prototype.exitFullscreen=function(){this.isFullscreen(this.iframe)&&(document.exitFullscreen||document.mozCancelFullScreen||document.webkitExitFullscreen||document.msExitFullscreen).call(document)},t.prototype.isFullscreen=function(t){return["fullscreenElement","webkitFullscreenElement","mozFullscreenScreenElement","msFullscreenElement"].some((function(e){return document[e]===t}))},t.prototype.setIframe=function(e,r,i){var a=this;if(!this.iframe){var o=document.createElement("iframe"),n=this.config.uniqueId?(0,s.addParamToUrl)(this.config.embedUrl,"uid",this.config.uniqueId):this.config.embedUrl;o.style.width="100%",o.style.height="100%",o.setAttribute("src",n),o.setAttribute("scrolling","no"),o.setAttribute("allowfullscreen","true");for(var l=this.element;l.firstChild;)l.removeChild(l.firstChild);l.appendChild(o),this.iframe=l.firstChild}if(e){if(!i){var d=this.validate(this.config);if(d)throw d}this.iframe.addEventListener("load",(function(){a.iframeLoaded=!0,a.load(r)}),!1),this.service.getNumberOfComponents()<=t.maxFrontLoadTimes&&(this.frontLoadHandler=function(){a.frontLoadSendConfig(a.config)},this.element.addEventListener("ready",this.frontLoadHandler,!1))}else this.iframe.addEventListener("load",(function(){return a.create()}),!1)},t.prototype.setComponentTitle=function(t){this.iframe&&(null==t?this.iframe.removeAttribute("title"):this.iframe.setAttribute("title",t))},t.prototype.setComponentTabIndex=function(t){this.element&&this.element.setAttribute("tabindex",null==t?"0":t.toString())},t.prototype.removeComponentTabIndex=function(t){this.element&&this.element.removeAttribute("tabindex")},t.findGroupIdFromEmbedUrl=function(t){var e,r=t.match(/groupId="?([^&]+)"?/);return r&&(e=r[1]),e},t.prototype.frontLoadSendConfig=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(r){switch(r.label){case 0:if(!t.accessToken)return[2];if(e=this.validate(t))throw e;if(null==this.iframe.contentWindow)return[2];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.post("/frontload/config",t,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent().body];case 3:throw r.sent().body;case 4:return[2]}}))}))},t.allowedEvents=["loaded","saved","rendered","saveAsTriggered","error","dataSelected","buttonClicked","info"],t.accessTokenAttribute="powerbi-access-token",t.embedUrlAttribute="powerbi-embed-url",t.nameAttribute="powerbi-name",t.typeAttribute="powerbi-type",t.defaultEmbedHostName="https://app.powerbi.com",t.maxFrontLoadTimes=2,t}();e.Embed=d},"./src/errors.ts":(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.EmbedUrlNotSupported=e.APINotSupportedForRDLError=void 0,e.APINotSupportedForRDLError="This API is currently not supported for RDL reports",e.EmbedUrlNotSupported="Embed URL is invalid for this scenario. Please use Power BI REST APIs to get the valid URL"},"./src/factories.ts":(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.routerFactory=e.wpmpFactory=e.hpmFactory=void 0;var i=r("./node_modules/window-post-message-proxy/dist/windowPostMessageProxy.js"),a=r("./node_modules/http-post-message/dist/httpPostMessage.js"),o=r("./node_modules/powerbi-router/dist/router.js"),n=r("./src/config.ts");e.hpmFactory=function(t,e,r,i,o){return void 0===r&&(r=n.default.version),void 0===i&&(i=n.default.type),new a.HttpPostMessage(t,{"x-sdk-type":i,"x-sdk-version":r,"x-sdk-wrapper-version":o},e)},e.wpmpFactory=function(t,e,r){return new i.WindowPostMessageProxy({processTrackingProperties:{addTrackingProperties:a.HttpPostMessage.addTrackingProperties,getTrackingProperties:a.HttpPostMessage.getTrackingProperties},isErrorMessage:a.HttpPostMessage.isErrorMessage,suppressWarnings:!0,name:t,logMessages:e,eventSourceOverrideWindow:r})},e.routerFactory=function(t){return new o.Router(t)}},"./src/page.ts":function(t,e,r){var i=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.Page=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=r("./src/visualDescriptor.ts"),l=r("./src/util.ts"),s=r("./src/errors.ts"),d=function(){function t(t,e,r,i,a,o,n,l,s,d){this.report=t,this.name=e,this.displayName=r,this.isActive=i,this.visibility=a,this.defaultSize=o,this.mobileSize=l,this.defaultDisplayOption=n,this.background=s,this.wallpaper=d}return t.prototype.getFilters=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.report.service.hpm.get("/report/pages/".concat(this.name,"/filters"),{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},t.prototype.updateFilters=function(t,e){return i(this,void 0,void 0,(function(){var r;return a(this,(function(i){switch(i.label){case 0:r={filtersOperation:t,filters:e},i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.report.service.hpm.post("/report/pages/".concat(this.name,"/filters"),r,{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 2:return[2,i.sent()];case 3:throw i.sent().body;case 4:return[2]}}))}))},t.prototype.removeFilters=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,this.updateFilters(o.FiltersOperations.RemoveAll)];case 1:return[2,t.sent()]}}))}))},t.prototype.setFilters=function(t){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.report.service.hpm.put("/report/pages/".concat(this.name,"/filters"),t,{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 1:return[2,e.sent()];case 2:throw e.sent().body;case 3:return[2]}}))}))},t.prototype.delete=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.report.service.hpm.delete("/report/pages/".concat(this.name),{},{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},t.prototype.setActive=function(){return i(this,void 0,void 0,(function(){var t;return a(this,(function(e){switch(e.label){case 0:t={name:this.name,displayName:null,isActive:!0},e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.report.service.hpm.put("/report/pages/active",t,{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 2:return[2,e.sent()];case 3:throw e.sent().body;case 4:return[2]}}))}))},t.prototype.setDisplayName=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(r){switch(r.label){case 0:e={name:this.name,displayName:t},r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.report.service.hpm.put("/report/pages/".concat(this.name,"/name"),e,{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 2:return[2,r.sent()];case 3:throw r.sent().body;case 4:return[2]}}))}))},t.prototype.getVisuals=function(){return i(this,void 0,void 0,(function(){var t=this;return a(this,(function(e){switch(e.label){case 0:if((0,l.isRDLEmbed)(this.report.config.embedUrl))return[2,Promise.reject(s.APINotSupportedForRDLError)];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.report.service.hpm.get("/report/pages/".concat(this.name,"/visuals"),{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 2:return[2,e.sent().body.map((function(e){return new n.VisualDescriptor(t,e.name,e.title,e.type,e.layout)}))];case 3:throw e.sent().body;case 4:return[2]}}))}))},t.prototype.getVisualByName=function(t){return i(this,void 0,void 0,(function(){var e,r;return a(this,(function(i){switch(i.label){case 0:if((0,l.isRDLEmbed)(this.report.config.embedUrl))return[2,Promise.reject(s.APINotSupportedForRDLError)];i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.report.service.hpm.get("/report/pages/".concat(this.name,"/visuals"),{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 2:return e=i.sent(),(r=e.body.find((function(e){return e.name===t})))?[2,new n.VisualDescriptor(this,r.name,r.title,r.type,r.layout)]:[2,Promise.reject(o.CommonErrorCodes.NotFound)];case 3:throw i.sent().body;case 4:return[2]}}))}))},t.prototype.setVisualDisplayState=function(t,e){return i(this,void 0,void 0,(function(){var r;return a(this,(function(i){return r=this.name,[2,this.report.setVisualDisplayState(r,t,e)]}))}))},t.prototype.moveVisual=function(t,e,r,o){return i(this,void 0,void 0,(function(){var i;return a(this,(function(a){return i=this.name,[2,this.report.moveVisual(i,t,e,r,o)]}))}))},t.prototype.resizeVisual=function(t,e,r){return i(this,void 0,void 0,(function(){var i;return a(this,(function(a){return i=this.name,[2,this.report.resizeVisual(i,t,e,r)]}))}))},t.prototype.resizePage=function(t,e,r){return i(this,void 0,void 0,(function(){return a(this,(function(i){return this.isActive?[2,this.report.resizeActivePage(t,e,r)]:[2,Promise.reject("Cannot resize the page. Only the active page can be resized")]}))}))},t.prototype.getSlicers=function(){return i(this,void 0,void 0,(function(){var t=this;return a(this,(function(e){switch(e.label){case 0:if((0,l.isRDLEmbed)(this.report.config.embedUrl))return[2,Promise.reject(s.APINotSupportedForRDLError)];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.report.service.hpm.get("/report/pages/".concat(this.name,"/visuals"),{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 2:return[2,e.sent().body.filter((function(t){return"slicer"===t.type})).map((function(e){return new n.VisualDescriptor(t,e.name,e.title,e.type,e.layout)}))];case 3:throw e.sent().body;case 4:return[2]}}))}))},t.prototype.hasLayout=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(r){switch(r.label){case 0:if((0,l.isRDLEmbed)(this.report.config.embedUrl))return[2,Promise.reject(s.APINotSupportedForRDLError)];e=o.LayoutType[t],r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.report.service.hpm.get("/report/pages/".concat(this.name,"/layoutTypes/").concat(e),{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)];case 2:return[2,r.sent().body];case 3:throw r.sent().body;case 4:return[2]}}))}))},t}();e.Page=d},"./src/qna.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},n=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.Qna=void 0;var l=r("./node_modules/powerbi-models/dist/models.js"),s=function(t){function e(r,i,a,o,n){var l=t.call(this,r,i,a,void 0,o,n)||this;return l.loadPath="/qna/load",l.phasedLoadPath="/qna/prepare",Array.prototype.push.apply(l.allowedEvents,e.allowedEvents),l}return a(e,t),e.prototype.getId=function(){return null},e.prototype.setQuestion=function(t){return o(this,void 0,void 0,(function(){var e;return n(this,(function(r){switch(r.label){case 0:e={question:t},r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.post("/qna/interpret",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent()];case 3:throw r.sent().body;case 4:return[2]}}))}))},e.prototype.configChanged=function(t){},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"qnaEmbed"},e.prototype.validate=function(t){return(0,l.validateLoadQnaConfiguration)(t)},e.type="Qna",e.allowedEvents=["loaded","visualRendered"],e}(r("./src/embed.ts").Embed);e.Qna=s},"./src/quickCreate.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},n=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.QuickCreate=void 0;var l=r("./node_modules/powerbi-models/dist/models.js"),s=function(t){function e(e,r,i,a,l){var s=t.call(this,e,r,i,void 0,a,l)||this;return e.router.post("/reports/".concat(s.config.uniqueId,"/eventHooks/:eventName"),(function(t,r){return o(s,void 0,void 0,(function(){var i;return n(this,(function(a){switch(a.label){case 0:return"newAccessToken"===t.params.eventName?[3,1]:[3,3];case 1:return t.body=t.body||{},t.body.report=this,[4,e.invokeSDKHook(null===(i=this.eventHooks)||void 0===i?void 0:i.accessTokenProvider,t,r)];case 2:return a.sent(),[3,4];case 3:return[3,4];case 4:return[2]}}))}))})),s}return a(e,t),e.prototype.getId=function(){return null},e.prototype.validate=function(t){return(0,l.validateQuickCreate)(t)},e.prototype.configChanged=function(t){t||(this.createConfig=this.config)},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"quickCreate"},e.prototype.create=function(){var t;return o(this,void 0,void 0,(function(){var e,r;return n(this,(function(i){switch(i.label){case 0:if(e=(0,l.validateQuickCreate)(this.createConfig))throw e;i.label=1;case 1:return i.trys.push([1,3,,4]),r={uid:this.config.uniqueId,sdkSessionId:this.service.getSdkSessionId()},(null===(t=this.eventHooks)||void 0===t?void 0:t.accessTokenProvider)&&(r.tokenProviderSupplied=!0),[4,this.service.hpm.post("/quickcreate",this.createConfig,r,this.iframe.contentWindow)];case 2:return[2,i.sent().body];case 3:throw i.sent().body;case 4:return[2]}}))}))},e}(r("./src/embed.ts").Embed);e.QuickCreate=s},"./src/report.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},n=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},l=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var i,a=0,o=e.length;a<o;a++)!i&&a in e||(i||(i=Array.prototype.slice.call(e,0,a)),i[a]=e[a]);return t.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.Report=void 0;var s=r("./node_modules/powerbi-models/dist/models.js"),d=r("./src/embed.ts"),u=r("./src/util.ts"),c=r("./src/errors.ts"),p=r("./src/page.ts"),f=r("./src/bookmarksManager.ts"),h=function(t){function e(r,i,a,l,s,d){var u=this,c=a;return(u=t.call(this,r,i,c,d,l,s)||this).loadPath="/report/load",u.phasedLoadPath="/report/prepare",Array.prototype.push.apply(u.allowedEvents,e.allowedEvents),u.bookmarksManager=new f.BookmarksManager(r,c,u.iframe),r.router.post("/reports/".concat(u.config.uniqueId,"/eventHooks/:eventName"),(function(t,e){return o(u,void 0,void 0,(function(){var i,a;return n(this,(function(o){switch(o.label){case 0:switch(t.params.eventName){case"preQuery":return[3,1];case"newAccessToken":return[3,3]}return[3,5];case 1:return t.body=t.body||{},t.body.report=this,[4,r.invokeSDKHook(null===(i=this.eventHooks)||void 0===i?void 0:i.applicationContextProvider,t,e)];case 2:case 4:return o.sent(),[3,6];case 3:return t.body=t.body||{},t.body.report=this,[4,r.invokeSDKHook(null===(a=this.eventHooks)||void 0===a?void 0:a.accessTokenProvider,t,e)];case 5:return[3,6];case 6:return[2]}}))}))})),u}return a(e,t),e.findIdFromEmbedUrl=function(t){var e,r=t.match(/reportId="?([^&]+)"?/);return r&&(e=r[1]),e},e.prototype.render=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.service.hpm.post("/report/render",t,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,e.sent().body];case 2:throw e.sent().body;case 3:return[2]}}))}))},e.prototype.addPage=function(t){return o(this,void 0,void 0,(function(){var e,r,i;return n(this,(function(a){switch(a.label){case 0:e={displayName:t},a.label=1;case 1:return a.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/addPage",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return r=a.sent(),i=r.body,[2,new p.Page(this,i.name,i.displayName,i.isActive,i.visibility,i.defaultSize,i.defaultDisplayOption)];case 3:throw a.sent().body;case 4:return[2]}}))}))},e.prototype.deletePage=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.service.hpm.delete("/report/pages/".concat(t),{},{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,e.sent().body];case 2:throw e.sent().body;case 3:return[2]}}))}))},e.prototype.renamePage=function(t,e){return o(this,void 0,void 0,(function(){var r;return n(this,(function(i){switch(i.label){case 0:r={name:t,displayName:e},i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.service.hpm.put("/report/pages/".concat(t,"/name"),r,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,i.sent().body];case 3:throw i.sent().body;case 4:return[2]}}))}))},e.prototype.getFilters=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.service.hpm.get("/report/filters",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,t.sent().body];case 3:throw t.sent().body;case 4:return[2]}}))}))},e.prototype.updateFilters=function(t,e){return o(this,void 0,void 0,(function(){var r;return n(this,(function(i){switch(i.label){case 0:r={filtersOperation:t,filters:e},i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/filters",r,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,i.sent()];case 3:throw i.sent().body;case 4:return[2]}}))}))},e.prototype.removeFilters=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){return(0,u.isRDLEmbed)(this.config.embedUrl)?[2,Promise.reject(c.APINotSupportedForRDLError)]:[2,this.updateFilters(s.FiltersOperations.RemoveAll)]}))}))},e.prototype.setFilters=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.service.hpm.put("/report/filters",t,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,e.sent()];case 3:throw e.sent().body;case 4:return[2]}}))}))},e.prototype.getId=function(){var t=this.config,r=t.id||this.element.getAttribute(e.reportIdAttribute)||e.findIdFromEmbedUrl(t.embedUrl);if("string"!=typeof r||0===r.length)throw new Error("Report id is required, but it was not found. You must provide an id either as part of embed configuration or as attribute '".concat(e.reportIdAttribute,"'."));return r},e.prototype.getPages=function(){return o(this,void 0,void 0,(function(){var t=this;return n(this,(function(e){switch(e.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.service.hpm.get("/report/pages",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,e.sent().body.map((function(e){return new p.Page(t,e.name,e.displayName,e.isActive,e.visibility,e.defaultSize,e.defaultDisplayOption,e.mobileSize,e.background,e.wallpaper)}))];case 3:throw e.sent().body;case 4:return[2]}}))}))},e.prototype.getPageByName=function(t){return o(this,void 0,void 0,(function(){var e,r;return n(this,(function(i){switch(i.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.service.hpm.get("/report/pages",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return e=i.sent(),(r=e.body.find((function(e){return e.name===t})))?[2,new p.Page(this,r.name,r.displayName,r.isActive,r.visibility,r.defaultSize,r.defaultDisplayOption,r.mobileSize,r.background,r.wallpaper)]:[2,Promise.reject(s.CommonErrorCodes.NotFound)];case 3:throw i.sent().body;case 4:return[2]}}))}))},e.prototype.getActivePage=function(){return o(this,void 0,void 0,(function(){var t,e;return n(this,(function(r){switch(r.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.get("/report/pages",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return t=r.sent(),e=t.body.find((function(t){return t.isActive})),[2,new p.Page(this,e.name,e.displayName,e.isActive,e.visibility,e.defaultSize,e.defaultDisplayOption,e.mobileSize,e.background,e.wallpaper)];case 3:throw r.sent().body;case 4:return[2]}}))}))},e.prototype.page=function(t,e,r,i){return new p.Page(this,t,e,r,i)},e.prototype.print=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/print",null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,t.sent().body];case 3:throw t.sent().body;case 4:return[2]}}))}))},e.prototype.setPage=function(t){return o(this,void 0,void 0,(function(){var e;return n(this,(function(r){switch(r.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];e={name:t,displayName:null,isActive:!0},r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.put("/report/pages/active",e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent()];case 3:throw r.sent().body;case 4:return[2]}}))}))},e.prototype.updateSettings=function(t){var e,r;return o(this,void 0,void 0,(function(){var i,a,o,l=this;return n(this,(function(n){switch(n.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl)&&null!=t.customLayout)return[2,Promise.reject(c.APINotSupportedForRDLError)];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.service.hpm.patch("/report/settings",t,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return i=n.sent(),a=null==t?void 0:t.extensions,this.commands=null!==(e=null==a?void 0:a.commands)&&void 0!==e?e:this.commands,this.groups=null!==(r=null==a?void 0:a.groups)&&void 0!==r?r:this.groups,o=null==t?void 0:t.extensions,Array.isArray(o)&&(this.commands=[],o.map((function(t){(null==t?void 0:t.command)&&l.commands.push(t.command)}))),[2,i];case 3:throw n.sent().body;case 4:return[2]}}))}))},e.prototype.validate=function(t){return(0,u.isRDLEmbed)(this.config.embedUrl)?(0,s.validatePaginatedReportLoad)(t):(0,s.validateReportLoad)(t)},e.prototype.configChanged=function(t){var r=this.config;this.isMobileSettings(r.settings)&&(r.embedUrl=(0,u.addParamToUrl)(r.embedUrl,"isMobile","true"));var i=this.element.getAttribute(e.filterPaneEnabledAttribute),a=this.element.getAttribute(e.navContentPaneEnabledAttribute),o={filterPaneEnabled:null==i?void 0:"false"!==i,navContentPaneEnabled:null==a?void 0:"false"!==a};this.config.settings=(0,u.assign)({},o,r.settings),t||(r.id=this.getId())},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"reportEmbed"},e.prototype.switchMode=function(t){return o(this,void 0,void 0,(function(){var e,r;return n(this,(function(i){switch(i.label){case 0:e="string"==typeof t?t:this.viewModeToString(t),r="/report/switchMode/"+e,i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.service.hpm.post(r,null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,i.sent().body];case 3:throw i.sent().body;case 4:return[2]}}))}))},e.prototype.refresh=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.service.hpm.post("/report/refresh",null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},e.prototype.isSaved=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return(0,u.isRDLEmbed)(this.config.embedUrl)?[2,Promise.reject(c.APINotSupportedForRDLError)]:[4,(0,u.isSavedInternal)(this.service.hpm,this.config.uniqueId,this.iframe.contentWindow)];case 1:return[2,t.sent()]}}))}))},e.prototype.applyTheme=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return(0,u.isRDLEmbed)(this.config.embedUrl)?[2,Promise.reject(c.APINotSupportedForRDLError)]:[4,this.applyThemeInternal(t)];case 1:return[2,e.sent()]}}))}))},e.prototype.resetTheme=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return(0,u.isRDLEmbed)(this.config.embedUrl)?[2,Promise.reject(c.APINotSupportedForRDLError)]:[4,this.applyThemeInternal({})];case 1:return[2,t.sent()]}}))}))},e.prototype.getTheme=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.service.hpm.get("/report/theme",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,t.sent().body];case 3:throw t.sent().body;case 4:return[2]}}))}))},e.prototype.resetPersistentFilters=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.service.hpm.delete("/report/userState",null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,t.sent()];case 2:throw t.sent().body;case 3:return[2]}}))}))},e.prototype.savePersistentFilters=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.service.hpm.post("/report/userState",null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,t.sent()];case 2:throw t.sent().body;case 3:return[2]}}))}))},e.prototype.arePersistentFiltersApplied=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.service.hpm.get("/report/isUserStateApplied",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},e.prototype.removeContextMenuCommand=function(t,e){return o(this,void 0,void 0,(function(){var r,i,a;return n(this,(function(o){switch(o.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];if(r=JSON.parse(JSON.stringify(this.commands)),-1===(i=this.findCommandMenuIndex("visualContextMenu",r,t,e)))throw s.CommonErrorCodes.NotFound;return delete r[i].extend.visualContextMenu,a={extensions:{commands:r,groups:this.groups}},[4,this.updateSettings(a)];case 1:return[2,o.sent()]}}))}))},e.prototype.addContextMenuCommand=function(t,e,r,i,a,l,d){return void 0===r&&(r=e),void 0===i&&(i=s.MenuLocation.Bottom),void 0===a&&(a=void 0),void 0===d&&(d=void 0),o(this,void 0,void 0,(function(){var o,s;return n(this,(function(n){switch(n.label){case 0:return(0,u.isRDLEmbed)(this.config.embedUrl)?[2,Promise.reject(c.APINotSupportedForRDLError)]:(o=this.createMenuCommand("visualContextMenu",t,e,r,i,a,l,d),s={extensions:{commands:o,groups:this.groups}},[4,this.updateSettings(s)]);case 1:return[2,n.sent()]}}))}))},e.prototype.removeOptionsMenuCommand=function(t,e){return o(this,void 0,void 0,(function(){var r,i,a;return n(this,(function(o){switch(o.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];if(r=JSON.parse(JSON.stringify(this.commands)),-1===(i=this.findCommandMenuIndex("visualOptionsMenu",r,t,e)))throw s.CommonErrorCodes.NotFound;return delete r[i].extend.visualOptionsMenu,delete r[i].icon,a={extensions:{commands:r,groups:this.groups}},[4,this.updateSettings(a)];case 1:return[2,o.sent()]}}))}))},e.prototype.addOptionsMenuCommand=function(t,e,r,i,a,l,d,p){return void 0===r&&(r=e),void 0===i&&(i=s.MenuLocation.Bottom),void 0===a&&(a=void 0),void 0===l&&(l=void 0),void 0===d&&(d=void 0),void 0===p&&(p=void 0),o(this,void 0,void 0,(function(){var o,s;return n(this,(function(n){switch(n.label){case 0:return(0,u.isRDLEmbed)(this.config.embedUrl)?[2,Promise.reject(c.APINotSupportedForRDLError)]:(o=this.createMenuCommand("visualOptionsMenu",t,e,r,i,a,l,d,p),s={extensions:{commands:o,groups:this.groups}},[4,this.updateSettings(s)]);case 1:return[2,n.sent()]}}))}))},e.prototype.setVisualDisplayState=function(t,e,r){return o(this,void 0,void 0,(function(){var i,a;return n(this,(function(o){switch(o.label){case 0:return[4,this.validateVisual(t,e)];case 1:return o.sent(),i={displayState:{mode:r}},a=this.buildLayoutSettingsObject(t,e,i),[2,this.updateSettings(a)]}}))}))},e.prototype.resizeVisual=function(t,e,r,i){return o(this,void 0,void 0,(function(){var a,o;return n(this,(function(n){switch(n.label){case 0:return[4,this.validateVisual(t,e)];case 1:return n.sent(),a={width:r,height:i},o=this.buildLayoutSettingsObject(t,e,a),[2,this.updateSettings(o)]}}))}))},e.prototype.resizeActivePage=function(t,e,r){return o(this,void 0,void 0,(function(){var i,a;return n(this,(function(o){return i={type:t,width:e,height:r},a={layoutType:s.LayoutType.Custom,customLayout:{pageSize:i}},[2,this.updateSettings(a)]}))}))},e.prototype.moveVisual=function(t,e,r,i,a){return o(this,void 0,void 0,(function(){var o,l;return n(this,(function(n){switch(n.label){case 0:return[4,this.validateVisual(t,e)];case 1:return n.sent(),o={x:r,y:i,z:a},l=this.buildLayoutSettingsObject(t,e,o),[2,this.updateSettings(l)]}}))}))},e.prototype.switchLayout=function(t){return o(this,void 0,void 0,(function(){var e,r,i,a;return n(this,(function(o){switch(o.label){case 0:if(e=this.isMobileSettings({layoutType:this.initialLayoutType}),r=this.isMobileSettings({layoutType:t}),e!==r)throw"Switching between mobile and desktop layouts is not supported. Please reset the embed container and re-embed with required layout.";return i={layoutType:t},[4,this.updateSettings(i)];case 1:return a=o.sent(),this.initialLayoutType=t,[2,a]}}))}))},e.prototype.createMenuCommand=function(t,e,r,i,a,o,n,s,d){var u={name:e,title:r,extend:{}};return u.extend[t]={title:i,menuLocation:a},"visualOptionsMenu"===t&&(u.icon=d),s&&(delete u.extend[t].menuLocation,u.extend[t].groupName=s),o&&(u.selector={$schema:"http://powerbi.com/product/schema#visualSelector",visualName:o}),n&&(u.selector={$schema:"http://powerbi.com/product/schema#visualTypeSelector",visualType:n}),l(l([],this.commands,!0),[u],!1)},e.prototype.findCommandMenuIndex=function(t,e,r,i){var a=-1;return e.some((function(e,o){return!(e.name!==r||!e.extend[t]||e.extend[t].title!==i||(a=o,0))})),a},e.prototype.buildLayoutSettingsObject=function(t,e,r){var i={layoutType:s.LayoutType.Custom,customLayout:{pagesLayout:{}}};return i.customLayout.pagesLayout[t]={visualsLayout:{}},i.customLayout.pagesLayout[t].visualsLayout[e]=r,i},e.prototype.validateVisual=function(t,e){return o(this,void 0,void 0,(function(){return n(this,(function(r){switch(r.label){case 0:return[4,this.getPageByName(t)];case 1:return[4,r.sent().getVisualByName(e)];case 2:return[2,r.sent()]}}))}))},e.prototype.applyThemeInternal=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.service.hpm.put("/report/theme",t,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,e.sent().body];case 2:throw e.sent().body;case 3:return[2]}}))}))},e.prototype.viewModeToString=function(t){var e;switch(t){case s.ViewMode.Edit:e="edit";break;case s.ViewMode.View:e="view"}return e},e.prototype.isMobileSettings=function(t){return t&&(t.layoutType===s.LayoutType.MobileLandscape||t.layoutType===s.LayoutType.MobilePortrait)},e.prototype.getZoom=function(){return o(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.service.hpm.get("/report/zoom",{uid:this.config.uniqueId},this.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},e.prototype.setZoom=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return[4,this.updateSettings({zoomLevel:t})];case 1:return e.sent(),[2]}}))}))},e.prototype.closeAllOverlays=function(){return o(this,void 0,void 0,(function(){var t;return n(this,(function(e){switch(e.label){case 0:if((0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/closeAllOverlays",null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,e.sent().body];case 3:return t=e.sent(),[2,Promise.reject(t)];case 4:return[2]}}))}))},e.prototype.clearSelectedVisuals=function(t){return o(this,void 0,void 0,(function(){var e;return n(this,(function(r){switch(r.label){case 0:if(t=!0===t,(0,u.isRDLEmbed)(this.config.embedUrl))return[2,Promise.reject(c.APINotSupportedForRDLError)];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.post("/report/clearSelectedVisuals/".concat(t.toString()),null,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent().body];case 3:return e=r.sent(),[2,Promise.reject(e)];case 4:return[2]}}))}))},e.allowedEvents=["filtersApplied","pageChanged","commandTriggered","swipeStart","swipeEnd","bookmarkApplied","dataHyperlinkClicked","visualRendered","visualClicked","selectionChanged","renderingStarted","blur"],e.reportIdAttribute="powerbi-report-id",e.filterPaneEnabledAttribute="powerbi-settings-filter-pane-enabled",e.navContentPaneEnabledAttribute="powerbi-settings-nav-content-pane-enabled",e.typeAttribute="powerbi-type",e.type="Report",e}(d.Embed);e.Report=h},"./src/service.ts":function(t,e,r){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,r=1,i=arguments.length;r<i;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i.apply(this,arguments)},a=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},o=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.Service=void 0;var n=r("./src/embed.ts"),l=r("./src/report.ts"),s=r("./src/create.ts"),d=r("./src/dashboard.ts"),u=r("./src/tile.ts"),c=r("./src/page.ts"),p=r("./src/qna.ts"),f=r("./src/visual.ts"),h=r("./src/util.ts"),v=r("./src/quickCreate.ts"),y=r("./src/config.ts"),m=function(){function t(e,r,i,a){void 0===a&&(a={});var o=this;this.registeredComponents={},this.wpmp=r(a.wpmpName,a.logMessages),this.hpm=e(this.wpmp,null,a.version,a.type,a.sdkWrapperVersion),this.router=i(this.wpmp),this.uniqueSessionId=h.generateUUID(),this.router.post("/reports/:uniqueId/events/:eventName",(function(t,e){var r={type:"report",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)})),this.router.post("/reports/:uniqueId/pages/:pageName/events/:eventName",(function(t,e){var r={type:"report",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)})),this.router.post("/reports/:uniqueId/pages/:pageName/visuals/:visualName/events/:eventName",(function(t,e){var r={type:"report",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)})),this.router.post("/dashboards/:uniqueId/events/:eventName",(function(t,e){var r={type:"dashboard",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)})),this.router.post("/tile/:uniqueId/events/:eventName",(function(t,e){var r={type:"tile",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)})),this.router.post("/qna/:uniqueId/events/:eventName",(function(t,e){var r={type:"qna",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)})),this.router.post("/ready/:uniqueId",(function(t,e){var r={type:"report",id:t.params.uniqueId,name:"ready",value:t.body};o.handleEvent(r)})),this.embeds=[],this.config=h.assign({},t.defaultConfig,a),this.config.autoEmbedOnContentLoaded&&this.enableAutoEmbed()}return t.prototype.createReport=function(t,e){e.type="create";var r=t,i=new s.Create(this,r,e);return r.powerBiEmbed=i,this.addOrOverwriteEmbed(i,t),i},t.prototype.quickCreate=function(t,e){e.type="quickCreate";var r=t,i=new v.QuickCreate(this,r,e);return r.powerBiEmbed=i,this.addOrOverwriteEmbed(i,t),i},t.prototype.init=function(t,e){var r=this;return void 0===e&&(e=void 0),t=t&&t instanceof HTMLElement?t:document.body,Array.prototype.slice.call(t.querySelectorAll("[".concat(n.Embed.embedUrlAttribute,"]"))).map((function(t){return r.embed(t,e)}))},t.prototype.embed=function(t,e){return void 0===e&&(e={}),this.embedInternal(t,e)},t.prototype.load=function(t,e){return void 0===e&&(e={}),this.embedInternal(t,e,!0,!1)},t.prototype.bootstrap=function(t,e){return this.embedInternal(t,e,!1,!0)},t.prototype.embedInternal=function(t,e,r,i){var a;void 0===e&&(e={});var o=t;if(o.powerBiEmbed){if(i)throw new Error("Attempted to bootstrap element ".concat(t.outerHTML,", but the element is already a powerbi element."));a=this.embedExisting(o,e,r)}else a=this.embedNew(o,e,r,i);return a},t.prototype.getNumberOfComponents=function(){return this.embeds?this.embeds.length:0},t.prototype.getSdkSessionId=function(){return this.uniqueSessionId},t.prototype.getSDKVersion=function(){return y.default.version},t.prototype.embedNew=function(t,e,r,a){var o=e.type||t.getAttribute(n.Embed.typeAttribute);if(!o){var s=i(i({},e),{accessToken:""});throw new Error("Attempted to embed using config ".concat(JSON.stringify(s)," on element ").concat(t.outerHTML,", but could not determine what type of component to embed. You must specify a type in the configuration or as an attribute such as '").concat(n.Embed.typeAttribute,'="').concat(l.Report.type.toLowerCase(),"\"'."))}e.type=o;var d=this.createEmbedComponent(o,t,e,r,a);return t.powerBiEmbed=d,this.addOrOverwriteEmbed(d,t),d},t.prototype.createEmbedComponent=function(e,r,i,a,o){var n=h.find((function(t){return e===t.type.toLowerCase()}),t.components);if(n)return new n(this,r,i,a,o);var l=h.find((function(t){return e.toLowerCase()===t.toLowerCase()}),Object.keys(this.registeredComponents));if(!l)throw new Error("Attempted to embed component of type: ".concat(e," but did not find any matching component.  Please verify the type you specified is intended."));return this.registeredComponents[l](this,r,i,a,o)},t.prototype.embedExisting=function(t,e,r){var a=h.find((function(e){return e.element===t}),this.embeds);if(!a){var o=i(i({},e),{accessToken:""});throw new Error("Attempted to embed using config ".concat(JSON.stringify(o)," on element ").concat(t.outerHTML," which already has embedded component associated, but could not find the existing component in the list of active components. This could indicate the embeds list is out of sync with the DOM, or the component is referencing the incorrect HTML element."))}if(e.type&&"qna"===e.type.toLowerCase())return this.embedNew(t,e);if("string"==typeof e.type&&e.type!==a.config.type){if("report"===e.type&&h.isCreate(a.config.type)){var n=new l.Report(this,t,e,!1,!1,t.powerBiEmbed.iframe);return a.populateConfig(e,!1),n.load(),t.powerBiEmbed=n,this.addOrOverwriteEmbed(a,t),n}throw o=i(i({},e),{accessToken:""}),new Error("Embedding on an existing element with a different type than the previous embed object is not supported.  Attempted to embed using config ".concat(JSON.stringify(o)," on element ").concat(t.outerHTML,", but the existing element contains an embed of type: ").concat(this.config.type," which does not match the new type: ").concat(e.type))}return a.populateConfig(e,!1),a.load(r),a},t.prototype.enableAutoEmbed=function(){var t=this;window.addEventListener("DOMContentLoaded",(function(e){return t.init(document.body)}),!1)},t.prototype.get=function(t){var e=t;if(!e.powerBiEmbed)throw new Error("You attempted to get an instance of powerbi component associated with element: ".concat(t.outerHTML," but there was no associated instance."));return e.powerBiEmbed},t.prototype.find=function(t){return h.find((function(e){return e.config.uniqueId===t}),this.embeds)},t.prototype.addOrOverwriteEmbed=function(t,e){this.embeds=this.embeds.filter((function(t){return t.element!==e})),this.embeds.push(t)},t.prototype.reset=function(t){var e=t;if(e.powerBiEmbed){var r=e.powerBiEmbed;r.frontLoadHandler&&r.element.removeEventListener("ready",r.frontLoadHandler,!1),r.allowedEvents.forEach((function(t){r.off(t)})),h.remove((function(t){return t===e.powerBiEmbed}),this.embeds),delete e.powerBiEmbed;var i=t.querySelector("iframe");i&&(void 0!==i.remove?i.remove():i.parentElement.removeChild(i))}},t.prototype.handleTileEvents=function(t){"tile"===t.type&&this.handleEvent(t)},t.prototype.invokeSDKHook=function(t,e,r){return a(this,void 0,void 0,(function(){var i,a;return o(this,(function(o){switch(o.label){case 0:if(!t)return r.send(404,null),[2];o.label=1;case 1:return o.trys.push([1,3,,4]),[4,t(e.body)];case 2:return i=o.sent(),r.send(200,i),[3,4];case 3:return a=o.sent(),r.send(400,null),console.error(a),[3,4];case 4:return[2]}}))}))},t.prototype.handleEvent=function(t){var e=h.find((function(e){return e.config.uniqueId===t.id}),this.embeds);if(e){var r=t.value;if("pageChanged"===t.name){var i="newPage",a=r[i];if(!a)throw new Error("Page model not found at 'event.value.".concat(i,"'."));r[i]=new c.Page(e,a.name,a.displayName,!0)}h.raiseCustomEvent(e.element,t.name,r)}},t.prototype.preload=function(t,e){var r=document.createElement("iframe");r.setAttribute("style","display:none;"),r.setAttribute("src",t.embedUrl),r.setAttribute("scrolling","no"),r.setAttribute("allowfullscreen","false");var i=e;return i||(i=document.getElementsByTagName("body")[0]),i.appendChild(r),r.onload=function(){h.raiseCustomEvent(r,"preloaded",{})},r},t.prototype.setSdkInfo=function(t,e){this.hpm.defaultHeaders["x-sdk-type"]=t,this.hpm.defaultHeaders["x-sdk-wrapper-version"]=e},t.prototype.register=function(e,r,i){var a=this;if(h.find((function(t){return e.toLowerCase()===t.type.toLowerCase()}),t.components))throw new Error("The component name is reserved. Cannot register a component with this name.");if(h.find((function(t){return e.toLowerCase()===t.toLowerCase()}),Object.keys(this.registeredComponents)))throw new Error("A component with this type is already registered.");this.registeredComponents[e]=r,i.forEach((function(t){if(!t.includes(":uniqueId")||!t.includes(":eventName"))throw new Error("Invalid router event URL");a.router.post(t,(function(t,r){var i={type:e,id:t.params.uniqueId,name:t.params.eventName,value:t.body};a.handleEvent(i)}))}))},t.components=[u.Tile,l.Report,d.Dashboard,p.Qna,f.Visual],t.defaultConfig={autoEmbedOnContentLoaded:!1,onError:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return console.log(t[0],t.slice(1))}},t}();e.Service=m},"./src/tile.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.Tile=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(t){function e(r,i,a,o,n){var l=this,s=a;return(l=t.call(this,r,i,s,void 0,o,n)||this).loadPath="/tile/load",Array.prototype.push.apply(l.allowedEvents,e.allowedEvents),l}return a(e,t),e.prototype.getId=function(){var t=this.config.id||e.findIdFromEmbedUrl(this.config.embedUrl);if("string"!=typeof t||0===t.length)throw new Error("Tile id is required, but it was not found. You must provide an id either as part of embed configuration.");return t},e.prototype.validate=function(t){var e=t;return(0,o.validateTileLoad)(e)},e.prototype.configChanged=function(t){t||(this.config.id=this.getId())},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"tileEmbed"},e.findIdFromEmbedUrl=function(t){var e,r=t.match(/tileId="?([^&]+)"?/);return r&&(e=r[1]),e},e.type="Tile",e.allowedEvents=["tileClicked","tileLoaded"],e}(r("./src/embed.ts").Embed);e.Tile=n},"./src/util.ts":function(t,e){var r=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},i=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};function a(t,e){if(!Array.isArray(e))throw new Error("You attempted to call find with second parameter that was not an array. You passed: ".concat(e));var r;return e.some((function(e,i){if(t(e))return r=i,!0})),r}function o(){var t=window.crypto||window.msCrypto,e=new Uint32Array(1);return t.getRandomValues(e),e[0]}Object.defineProperty(e,"__esModule",{value:!0}),e.isCreate=e.getTimeDiffInMilliseconds=e.getRandomValue=e.autoAuthInEmbedUrl=e.isRDLEmbed=e.isSavedInternal=e.addParamToUrl=e.generateUUID=e.createRandomString=e.assign=e.remove=e.find=e.findIndex=e.raiseCustomEvent=void 0,e.raiseCustomEvent=function(t,e,r){var i;"function"==typeof CustomEvent?i=new CustomEvent(e,{detail:r,bubbles:!0,cancelable:!0}):(i=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,r),t.dispatchEvent(i)},e.findIndex=a,e.find=function(t,e){return e[a(t,e)]},e.remove=function(t,e){var r=a(t,e);e.splice(r,1)},e.assign=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=t[0];if(null==r)throw new TypeError("Cannot convert undefined or null to object");for(var i=Object(r),a=1;a<arguments.length;a++){var o=arguments[a];if(null!=o)for(var n in o)o.hasOwnProperty(n)&&(i[n]=o[n])}return i},e.createRandomString=function(){return o().toString(36).substring(1)},e.generateUUID=function(){return(new Date).getTime(),"undefined"!=typeof performance&&"function"==typeof performance.now&&performance.now(),"xxxxxxxxxxxxxxxxxxxx".replace(/[xy]/g,(function(t){return(o()%16).toString(16)}))},e.addParamToUrl=function(t,e,r){var i=t.indexOf("?")>0?"&":"?";return t+(i+e+"=")+r},e.isSavedInternal=function(t,e,a){return r(this,void 0,void 0,(function(){return i(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,t.get("/report/hasUnsavedChanges",{uid:e},a)];case 1:return[2,!r.sent().body];case 2:throw r.sent().body;case 3:return[2]}}))}))},e.isRDLEmbed=function(t){return t&&t.toLowerCase().indexOf("/rdlembed?")>=0},e.autoAuthInEmbedUrl=function(t){return t&&decodeURIComponent(t).toLowerCase().indexOf("autoauth=true")>=0},e.getRandomValue=o,e.getTimeDiffInMilliseconds=function(t,e){return Math.abs(t.getTime()-e.getTime())},e.isCreate=function(t){return"create"===t||"quickcreate"===t}},"./src/visual.ts":function(t,e,r){var i,a=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},n=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.Visual=void 0;var l=r("./node_modules/powerbi-models/dist/models.js"),s=r("./src/report.ts"),d=r("./src/visualDescriptor.ts"),u=function(t){function e(e,r,i,a,o,n){return t.call(this,e,r,i,a,o,n)||this}return a(e,t),e.prototype.load=function(e){var r=this.config;if(r.accessToken){if("string"!=typeof r.pageName||0===r.pageName.length)throw new Error("Page name is required when embedding a visual.");if("string"!=typeof r.visualName||0===r.visualName.length)throw new Error("Visual name is required, but it was not found. You must provide a visual name as part of embed configuration.");var i=r.width?r.width:this.iframe.offsetWidth,a=r.height?r.height:this.iframe.offsetHeight,o={type:l.PageSizeType.Custom,width:i,height:a},n={};return n[r.pageName]={defaultLayout:{displayState:{mode:l.VisualContainerDisplayMode.Hidden}},visualsLayout:{}},n[r.pageName].visualsLayout[r.visualName]={displayState:{mode:l.VisualContainerDisplayMode.Visible},x:1,y:1,z:1,width:o.width,height:o.height},r.settings=r.settings||{},r.settings.filterPaneEnabled=!1,r.settings.navContentPaneEnabled=!1,r.settings.layoutType=l.LayoutType.Custom,r.settings.customLayout={displayOption:l.DisplayOption.FitToPage,pageSize:o,pagesLayout:n},this.config=r,t.prototype.load.call(this,e)}},e.prototype.getPages=function(){throw e.GetPagesNotSupportedError},e.prototype.setPage=function(t){throw e.SetPageNotSupportedError},e.prototype.render=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(t){throw e.RenderNotSupportedError}))}))},e.prototype.getVisualDescriptor=function(){return o(this,void 0,void 0,(function(){var t,e,r,i,a;return n(this,(function(o){switch(o.label){case 0:t=this.config,o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.service.hpm.get("/report/pages/".concat(t.pageName,"/visuals"),{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:if(e=o.sent(),0===(r=e.body.filter((function(e){return e.name===t.visualName}))).length)throw{message:"visualNotFound",detailedMessage:"Visual not found"};return i=r[0],a=this.page(t.pageName),[2,new d.VisualDescriptor(a,i.name,i.title,i.type,i.layout)];case 3:throw o.sent().body;case 4:return[2]}}))}))},e.prototype.getFilters=function(t){return o(this,void 0,void 0,(function(){var e;return n(this,(function(r){switch(r.label){case 0:e=this.getFiltersLevelUrl(t),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.service.hpm.get(e,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,r.sent().body];case 3:throw r.sent().body;case 4:return[2]}}))}))},e.prototype.updateFilters=function(t,e,r){return o(this,void 0,void 0,(function(){var i,a;return n(this,(function(o){switch(o.label){case 0:i={filtersOperation:t,filters:e},a=this.getFiltersLevelUrl(r),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.service.hpm.post(a,i,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,o.sent()];case 3:throw o.sent().body;case 4:return[2]}}))}))},e.prototype.setFilters=function(t,e){return o(this,void 0,void 0,(function(){var r;return n(this,(function(i){switch(i.label){case 0:r=this.getFiltersLevelUrl(e),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.service.hpm.put(r,t,{uid:this.config.uniqueId},this.iframe.contentWindow)];case 2:return[2,i.sent()];case 3:throw i.sent().body;case 4:return[2]}}))}))},e.prototype.removeFilters=function(t){return o(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return[4,this.updateFilters(l.FiltersOperations.RemoveAll,void 0,t)];case 1:return[2,e.sent()]}}))}))},e.prototype.getFiltersLevelUrl=function(t){var e=this.config;switch(t){case l.FiltersLevel.Report:return"/report/filters";case l.FiltersLevel.Page:return"/report/pages/".concat(e.pageName,"/filters");default:return"/report/pages/".concat(e.pageName,"/visuals/").concat(e.visualName,"/filters")}},e.type="visual",e.GetPagesNotSupportedError="Get pages is not supported while embedding a visual.",e.SetPageNotSupportedError="Set page is not supported while embedding a visual.",e.RenderNotSupportedError="render is not supported while embedding a visual.",e}(s.Report);e.Visual=u},"./src/visualDescriptor.ts":function(t,e,r){var i=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var r,i,a,o,n={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(a=2&o[0]?i.return:o[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,o[1])).done)return a;switch(i=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return n.label++,{value:o[1],done:!1};case 5:n.label++,i=o[1],o=[0];continue;case 7:o=n.ops.pop(),n.trys.pop();continue;default:if(!((a=(a=n.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){n=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){n.label=o[1];break}if(6===o[0]&&n.label<a[1]){n.label=a[1],a=o;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(o);break}a[2]&&n.ops.pop(),n.trys.pop();continue}o=e.call(t,n)}catch(t){o=[6,t],i=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.VisualDescriptor=void 0;var o=r("./node_modules/powerbi-models/dist/models.js"),n=function(){function t(t,e,r,i,a){this.name=e,this.title=r,this.type=i,this.layout=a,this.page=t}return t.prototype.getFilters=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.page.report.service.hpm.get("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/filters"),{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},t.prototype.updateFilters=function(t,e){return i(this,void 0,void 0,(function(){var r;return a(this,(function(i){switch(i.label){case 0:r={filtersOperation:t,filters:e},i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.page.report.service.hpm.post("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/filters"),r,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 2:return[2,i.sent()];case 3:throw i.sent().body;case 4:return[2]}}))}))},t.prototype.removeFilters=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,this.updateFilters(o.FiltersOperations.RemoveAll)];case 1:return[2,t.sent()]}}))}))},t.prototype.setFilters=function(t){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.page.report.service.hpm.put("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/filters"),t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 1:return[2,e.sent()];case 2:throw e.sent().body;case 3:return[2]}}))}))},t.prototype.exportData=function(t,e){return i(this,void 0,void 0,(function(){var r;return a(this,(function(i){switch(i.label){case 0:r={rows:e,exportDataType:t},i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.page.report.service.hpm.post("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/exportData"),r,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 2:return[2,i.sent().body];case 3:throw i.sent().body;case 4:return[2]}}))}))},t.prototype.setSlicerState=function(t){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.page.report.service.hpm.put("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/slicer"),t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 1:return[2,e.sent()];case 2:throw e.sent().body;case 3:return[2]}}))}))},t.prototype.getSlicerState=function(){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.page.report.service.hpm.get("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/slicer"),{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 1:return[2,t.sent().body];case 2:throw t.sent().body;case 3:return[2]}}))}))},t.prototype.clone=function(t){return void 0===t&&(t={}),i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.page.report.service.hpm.post("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/clone"),t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 1:return[2,e.sent().body];case 2:throw e.sent().body;case 3:return[2]}}))}))},t.prototype.sortBy=function(t){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.page.report.service.hpm.put("/report/pages/".concat(this.page.name,"/visuals/").concat(this.name,"/sortBy"),t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)];case 1:return[2,e.sent()];case 2:throw e.sent().body;case 3:return[2]}}))}))},t.prototype.moveVisual=function(t,e,r){return i(this,void 0,void 0,(function(){var i,o;return a(this,(function(a){return i=this.page.name,o=this.name,[2,this.page.report.moveVisual(i,o,t,e,r)]}))}))},t.prototype.setVisualDisplayState=function(t){return i(this,void 0,void 0,(function(){var e,r;return a(this,(function(i){return e=this.page.name,r=this.name,[2,this.page.report.setVisualDisplayState(e,r,t)]}))}))},t.prototype.resizeVisual=function(t,e){return i(this,void 0,void 0,(function(){var r,i;return a(this,(function(a){return r=this.page.name,i=this.name,[2,this.page.report.resizeVisual(r,i,t,e)]}))}))},t}();e.VisualDescriptor=n},"./node_modules/window-post-message-proxy/dist/windowPostMessageProxy.js":function(t){var e;e=function(){return function(t){var e={};function r(i){if(e[i])return e[i].exports;var a=e[i]={exports:{},id:i,loaded:!1};return t[i].call(a.exports,a,a.exports,r),a.loaded=!0,a.exports}return r.m=t,r.c=e,r.p="",r(0)}([function(t,e){"use strict";var r=function(){function t(e){var r=this;void 0===e&&(e={processTrackingProperties:{addTrackingProperties:t.defaultAddTrackingProperties,getTrackingProperties:t.defaultGetTrackingProperties},isErrorMessage:t.defaultIsErrorMessage,receiveWindow:window,name:t.createRandomString()}),this.pendingRequestPromises={},this.addTrackingProperties=e.processTrackingProperties&&e.processTrackingProperties.addTrackingProperties||t.defaultAddTrackingProperties,this.getTrackingProperties=e.processTrackingProperties&&e.processTrackingProperties.getTrackingProperties||t.defaultGetTrackingProperties,this.isErrorMessage=e.isErrorMessage||t.defaultIsErrorMessage,this.receiveWindow=e.receiveWindow||window,this.name=e.name||t.createRandomString(),this.logMessages=e.logMessages||!1,this.eventSourceOverrideWindow=e.eventSourceOverrideWindow,this.suppressWarnings=e.suppressWarnings||!1,this.logMessages&&console.log("new WindowPostMessageProxy created with name: "+this.name+" receiving on window: "+this.receiveWindow.document.title),this.handlers=[],this.windowMessageHandler=function(t){return r.onMessageReceived(t)},this.start()}return t.defaultAddTrackingProperties=function(e,r){return e[t.messagePropertyName]=r,e},t.defaultGetTrackingProperties=function(e){return e[t.messagePropertyName]},t.defaultIsErrorMessage=function(t){return!!t.error},t.createDeferred=function(){var t={resolve:null,reject:null,promise:null},e=new Promise((function(e,r){t.resolve=e,t.reject=r}));return t.promise=e,t},t.createRandomString=function(){var t=window.crypto||window.msCrypto,e=new Uint32Array(1);return t.getRandomValues(e),e[0].toString(36).substring(1)},t.prototype.addHandler=function(t){this.handlers.push(t)},t.prototype.removeHandler=function(t){var e=this.handlers.indexOf(t);if(-1===e)throw new Error("You attempted to remove a handler but no matching handler was found.");this.handlers.splice(e,1)},t.prototype.start=function(){this.receiveWindow.addEventListener("message",this.windowMessageHandler)},t.prototype.stop=function(){this.receiveWindow.removeEventListener("message",this.windowMessageHandler)},t.prototype.postMessage=function(e,r){var i={id:t.createRandomString()};this.addTrackingProperties(r,i),this.logMessages&&(console.log(this.name+" Posting message:"),console.log(JSON.stringify(r,null,"  "))),e.postMessage(r,"*");var a=t.createDeferred();return this.pendingRequestPromises[i.id]=a,a.promise},t.prototype.sendResponse=function(t,e,r){this.addTrackingProperties(e,r),this.logMessages&&(console.log(this.name+" Sending response:"),console.log(JSON.stringify(e,null,"  "))),t.postMessage(e,"*")},t.prototype.onMessageReceived=function(t){var e=this;this.logMessages&&(console.log(this.name+" Received message:"),console.log("type: "+t.type),console.log(JSON.stringify(t.data,null,"  ")));var r=this.eventSourceOverrideWindow||t.source,i=t.data;if("object"==typeof i){var a,o;try{a=this.getTrackingProperties(i)}catch(t){this.suppressWarnings||console.warn("Proxy("+this.name+"): Error occurred when attempting to get tracking properties from incoming message:",JSON.stringify(i,null,"  "),"Error: ",t)}if(a&&(o=this.pendingRequestPromises[a.id]),o){var n=!0;try{n=this.isErrorMessage(i)}catch(t){console.warn("Proxy("+this.name+") Error occurred when trying to determine if message is consider an error response. Message: ",JSON.stringify(i,null,""),"Error: ",t)}n?o.reject(i):o.resolve(i),delete this.pendingRequestPromises[a.id]}else this.handlers.some((function(t){var o=!1;try{o=t.test(i)}catch(t){e.suppressWarnings||console.warn("Proxy("+e.name+"): Error occurred when handler was testing incoming message:",JSON.stringify(i,null,"  "),"Error: ",t)}if(o){var n=void 0;try{n=Promise.resolve(t.handle(i))}catch(t){e.suppressWarnings||console.warn("Proxy("+e.name+"): Error occurred when handler was processing incoming message:",JSON.stringify(i,null,"  "),"Error: ",t),n=Promise.resolve()}return n.then((function(t){if(!t){var o="Handler for message: "+JSON.stringify(i,null,"  ")+" did not return a response message. The default response message will be returned instead.";e.suppressWarnings||console.warn("Proxy("+e.name+"): "+o),t={warning:o}}e.sendResponse(r,t,a)})),!0}}))||this.suppressWarnings||console.warn("Proxy("+this.name+") did not handle message. Handlers: "+this.handlers.length+"  Message: "+JSON.stringify(i,null,"")+".")}else this.suppressWarnings||console.warn("Proxy("+this.name+"): Received message that was not an object. Discarding message")},t.messagePropertyName="windowPostMessageProxy",t}();e.WindowPostMessageProxy=r}])},t.exports=e()}},e={};function r(i){var a=e[i];if(void 0!==a)return a.exports;var o=e[i]={exports:{}};return t[i].call(o.exports,o,o.exports,r),o.exports}var i={};return(()=>{var t=i;Object.defineProperty(t,"__esModule",{value:!0}),t.RelativeTimeFilterBuilder=t.RelativeDateFilterBuilder=t.TopNFilterBuilder=t.AdvancedFilterBuilder=t.BasicFilterBuilder=t.Create=t.QuickCreate=t.VisualDescriptor=t.Visual=t.Qna=t.Page=t.Embed=t.Tile=t.Dashboard=t.Report=t.models=t.factories=t.service=void 0;var e=r("./node_modules/powerbi-models/dist/models.js");t.models=e;var a=r("./src/service.ts");t.service=a;var o=r("./src/factories.ts");t.factories=o;var n=r("./src/report.ts");Object.defineProperty(t,"Report",{enumerable:!0,get:function(){return n.Report}});var l=r("./src/dashboard.ts");Object.defineProperty(t,"Dashboard",{enumerable:!0,get:function(){return l.Dashboard}});var s=r("./src/tile.ts");Object.defineProperty(t,"Tile",{enumerable:!0,get:function(){return s.Tile}});var d=r("./src/embed.ts");Object.defineProperty(t,"Embed",{enumerable:!0,get:function(){return d.Embed}});var u=r("./src/page.ts");Object.defineProperty(t,"Page",{enumerable:!0,get:function(){return u.Page}});var c=r("./src/qna.ts");Object.defineProperty(t,"Qna",{enumerable:!0,get:function(){return c.Qna}});var p=r("./src/visual.ts");Object.defineProperty(t,"Visual",{enumerable:!0,get:function(){return p.Visual}});var f=r("./src/visualDescriptor.ts");Object.defineProperty(t,"VisualDescriptor",{enumerable:!0,get:function(){return f.VisualDescriptor}});var h=r("./src/quickCreate.ts");Object.defineProperty(t,"QuickCreate",{enumerable:!0,get:function(){return h.QuickCreate}});var v=r("./src/create.ts");Object.defineProperty(t,"Create",{enumerable:!0,get:function(){return v.Create}});var y=r("./src/FilterBuilders/index.ts");Object.defineProperty(t,"BasicFilterBuilder",{enumerable:!0,get:function(){return y.BasicFilterBuilder}}),Object.defineProperty(t,"AdvancedFilterBuilder",{enumerable:!0,get:function(){return y.AdvancedFilterBuilder}}),Object.defineProperty(t,"TopNFilterBuilder",{enumerable:!0,get:function(){return y.TopNFilterBuilder}}),Object.defineProperty(t,"RelativeDateFilterBuilder",{enumerable:!0,get:function(){return y.RelativeDateFilterBuilder}}),Object.defineProperty(t,"RelativeTimeFilterBuilder",{enumerable:!0,get:function(){return y.RelativeTimeFilterBuilder}});var m=new a.Service(o.hpmFactory,o.wpmpFactory,o.routerFactory);window.powerbi&&window.powerBISDKGlobalServiceInstanceName?window[window.powerBISDKGlobalServiceInstanceName]=m:window.powerbi=m})(),i})(),t.exports=e()},700:(t,e,r)=>{var i=r(379),a=r(206);"string"==typeof(a=a.__esModule?a.default:a)&&(a=[[t.id,a,""]]);i(a,{insert:"head",singleton:!1}),t.exports=a.locals||{}},379:(t,e,r)=>{"use strict";var i,a=function(){var t={};return function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}t[e]=r}return t[e]}}(),o=[];function n(t){for(var e=-1,r=0;r<o.length;r++)if(o[r].identifier===t){e=r;break}return e}function l(t,e){for(var r={},i=[],a=0;a<t.length;a++){var l=t[a],s=e.base?l[0]+e.base:l[0],d=r[s]||0,u="".concat(s," ").concat(d);r[s]=d+1;var c=n(u),p={css:l[1],media:l[2],sourceMap:l[3]};-1!==c?(o[c].references++,o[c].updater(p)):o.push({identifier:u,updater:v(p,e),references:1}),i.push(u)}return i}function s(t){var e=document.createElement("style"),i=t.attributes||{};if(void 0===i.nonce){var o=r.nc;o&&(i.nonce=o)}if(Object.keys(i).forEach((function(t){e.setAttribute(t,i[t])})),"function"==typeof t.insert)t.insert(e);else{var n=a(t.insert||"head");if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(e)}return e}var d,u=(d=[],function(t,e){return d[t]=e,d.filter(Boolean).join("\n")});function c(t,e,r,i){var a=r?"":i.media?"@media ".concat(i.media," {").concat(i.css,"}"):i.css;if(t.styleSheet)t.styleSheet.cssText=u(e,a);else{var o=document.createTextNode(a),n=t.childNodes;n[e]&&t.removeChild(n[e]),n.length?t.insertBefore(o,n[e]):t.appendChild(o)}}function p(t,e,r){var i=r.css,a=r.media,o=r.sourceMap;if(a?t.setAttribute("media",a):t.removeAttribute("media"),o&&"undefined"!=typeof btoa&&(i+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleSheet)t.styleSheet.cssText=i;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(i))}}var f=null,h=0;function v(t,e){var r,i,a;if(e.singleton){var o=h++;r=f||(f=s(e)),i=c.bind(null,r,o,!1),a=c.bind(null,r,o,!0)}else r=s(e),i=p.bind(null,r,e),a=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(r)};return i(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;i(t=e)}else a()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=(void 0===i&&(i=Boolean(window&&document&&document.all&&!window.atob)),i));var r=l(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var i=0;i<r.length;i++){var a=n(r[i]);o[a].references--}for(var s=l(t,e),d=0;d<r.length;d++){var u=n(r[d]);0===o[u].references&&(o[u].updater(),o.splice(u,1))}r=s}}}},112:function(t,e,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r),Object.defineProperty(t,i,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),a=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||i(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),window.__webpack_public_path__=document.querySelector("body").getAttribute("data-base-url")+"nbextensions/powerbiclient",a(r(607),e)},607:function(t,e,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r),Object.defineProperty(t,i,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),a=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||i(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),a(r(412),e),a(r(125),e),a(r(759),e)},759:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.QuickVisualizeView=e.QuickVisualizeModel=void 0;const i=r(146),a=r(957),o=r(412),n=r(593);r(700);const l=a.models.TokenType.Aad;class s extends i.DOMWidgetModel{defaults(){return Object.assign(Object.assign({},super.defaults()),{_model_name:s.model_name,_model_module:s.model_module,_model_module_version:s.model_module_version,_view_name:s.view_name,_view_module:s.view_module,_view_module_version:s.view_module_version,_embed_config:{},_event_data:{event_name:null,event_details:null},_saved_report_id:null,_embedded:!1,_token_expired:!1,_init_error:null,container_height:0,container_width:0})}}e.QuickVisualizeModel=s,s.model_name="QuickVisualizeModel",s.model_module=o.MODULE_NAME,s.model_module_version=o.MODULE_VERSION,s.view_name="QuickVisualizeView",s.view_module=o.MODULE_NAME,s.view_module_version=o.MODULE_VERSION;class d extends i.DOMWidgetView{render(){const t=document.createElement("div");t.style.visibility="hidden",this.el.appendChild(t),this.quickCreateContainer=t,this.embedConfigChanged(),this.model.on("change:_embed_config",this.embedConfigChanged,this),this.model.on("change:container_height",this.containerSizeChanged,this),this.model.on("change:container_width",this.containerSizeChanged,this)}containerSizeChanged(){this.quickCreateContainer&&(this.quickCreateContainer.style.height=`${this.model.get("container_height")}px`,this.quickCreateContainer.style.width=`${this.model.get("container_width")}px`)}setTokenExpiredFlag(){this.model.set("_token_expired",!0),this.touch()}embedConfigChanged(){const t=this.model.get("_embed_config"),e=t;if(!e)return this.model.set("_init_error","Embed configuration is missing"),void this.touch();if(e.type="quickCreate",e.tokenType=l,e.embedUrl="https://app.powerbi.com/quickCreate",e.reportCreationMode="QuickExplore",this.quickCreate)return this.quickCreate.config.accessToken!==e.accessToken&&(this.quickCreate.setAccessToken(e.accessToken),e.accessToken&&n.setTokenExpirationListener(e.accessToken,this)),this.model.set("_embedded",!0),void this.touch();if(!e.accessToken||n.getTokenExpirationTimeout(e.accessToken)<=0)this.setTokenExpiredFlag();else{this.quickCreate=n.powerbi.quickCreate(this.quickCreateContainer,e);try{setTimeout((()=>{e.accessToken&&n.setTokenExpirationListener(t.accessToken,this);let r=this.model.get("container_width"),i=this.model.get("container_height");r&&i||(r=this.el.getBoundingClientRect().width||980,i=.5625*r),this.quickCreateContainer.style.width=`${r}px`,this.quickCreateContainer.style.height=`${i}px`,this.quickCreateContainer.style.visibility="visible"}),500)}catch(t){console.error(t)}this.quickCreate.on("loaded",(()=>{console.log("Loaded"),this.model.set("_event_data",{event_name:"loaded",event_details:null}),this.touch()})),this.quickCreate.on("rendered",(()=>{console.log("Rendered"),this.model.set("_event_data",{event_name:"rendered",event_details:null}),this.touch()})),this.quickCreate.on("saved",(t=>{var e,r,i;console.log("Report saved to workspace");const a={reportObjectId:null===(e=null==t?void 0:t.detail)||void 0===e?void 0:e.reportObjectId,reportName:null===(r=null==t?void 0:t.detail)||void 0===r?void 0:r.reportName};this.model.set("_event_data",{event_name:"saved",event_details:a}),this.touch(),this.model.set("_saved_report_id",null===(i=null==t?void 0:t.detail)||void 0===i?void 0:i.reportObjectId),this.touch()})),this.quickCreate.on("error",(t=>{var e;if("visible"===this.quickCreateContainer.style.visibility)return;const r=null===(e=t)||void 0===e?void 0:e.detail;this.model.set("_init_error",`${null==r?void 0:r.message} - ${null==r?void 0:r.detailedMessage}`),this.touch()})),this.model.set("_embedded",!0),this.touch()}}}e.QuickVisualizeView=d},125:function(t,e,r){"use strict";var i=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))},a=this&&this.__rest||function(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(i=Object.getOwnPropertySymbols(t);a<i.length;a++)e.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(t,i[a])&&(r[i[a]]=t[i[a]])}return r};Object.defineProperty(e,"__esModule",{value:!0}),e.ReportView=e.ReportModel=void 0;const o=r(146),n=r(957),l=r(412);r(700);const s=r(593),d={pageName:void 0,visualName:void 0,rows:void 0,exportDataType:void 0},u={filters:[],request_completed:!0},c="Power BI report is not embedded";class p extends o.DOMWidgetModel{defaults(){return Object.assign(Object.assign({},super.defaults()),{_model_name:p.model_name,_model_module:p.model_module,_model_module_version:p.model_module_version,_view_name:p.view_name,_view_module:p.view_module,_view_module_version:p.view_module_version,_embed_config:{},_embedded:!1,container_height:0,container_width:0,_export_visual_data_request:d,_visual_data:null,_event_data:{event_name:null,event_details:null},_get_filters_request:!1,_report_filters:[],_report_filters_request:u,_get_pages_request:!1,_report_pages:[],_get_visuals_page_name:null,_page_visuals:[],_report_bookmark_name:null,_get_bookmarks_request:!1,_report_bookmarks:[],_report_active_page:null,_token_expired:!1,_client_error:null,_init_error:null})}}e.ReportModel=p,p.serializers=Object.assign({},o.DOMWidgetModel.serializers),p.model_name="ReportModel",p.model_module=l.MODULE_NAME,p.model_module_version=l.MODULE_VERSION,p.view_name="ReportView",p.view_module=l.MODULE_NAME,p.view_module_version=l.MODULE_VERSION;class f extends o.DOMWidgetView{render(){const t=document.createElement("div");t.style.visibility="hidden",this.el.appendChild(t),this.reportContainer=t,this.embedConfigChanged(),this.model.on("change:_embed_config",this.embedConfigChanged,this),this.model.on("change:container_height",this.containerSizeChanged,this),this.model.on("change:container_width",this.containerSizeChanged,this),this.model.on("change:_export_visual_data_request",this.exportVisualDataRequestChanged,this),this.model.on("change:_get_filters_request",this.getFiltersRequestChanged,this),this.model.on("change:_report_filters_request",this.reportFiltersChanged,this),this.model.on("change:_get_pages_request",this.getPagesRequestChanged,this),this.model.on("change:_get_visuals_page_name",this.getVisualsPageNameChanged,this),this.model.on("change:_report_bookmark_name",this.reportBookmarkNameChanged,this),this.model.on("change:_get_bookmarks_request",this.getBookmarksRequestChanged,this),this.model.on("change:_report_active_page",this.reportActivePageChanged,this)}containerSizeChanged(){this.reportContainer&&(this.reportContainer.style.height=`${this.model.get("container_height")}px`,this.reportContainer.style.width=`${this.model.get("container_width")}px`)}setTokenExpiredFlag(){this.model.set("_token_expired",!0),this.touch()}embedConfigChanged(){const t=this.model.get("_embed_config"),e=t;if(this.report){const t=e.accessToken;return this.report.config.embedUrl===e.embedUrl&&this.report.config.accessToken!==e.accessToken&&(this.report.setAccessToken(t),e.accessToken&&s.setTokenExpirationListener(e.accessToken,this)),this.model.set("_embedded",!0),void this.touch()}if(!e.accessToken||s.getTokenExpirationTimeout(e.accessToken)<=0)return void this.setTokenExpiredFlag();const r=t.viewMode!==n.models.ViewMode.View&&t.viewMode!==n.models.ViewMode.Edit;this.report=r?s.powerbi.createReport(this.reportContainer,e):s.powerbi.load(this.reportContainer,e);let a=9/16;this.report.on("loaded",(()=>i(this,void 0,void 0,(function*(){console.log("Loaded"),e.accessToken&&s.setTokenExpirationListener(t.accessToken,this);try{if(!r){const{width:t,height:e}=yield s.getActivePageSize(this.report);t&&e?a=e/t:console.error("Invalid report size")}let t=this.model.get("container_width"),e=this.model.get("container_height");t&&e||(t=this.el.getBoundingClientRect().width||980,e=t*a),this.reportContainer.style.width=`${t}px`,this.reportContainer.style.height=`${e}px`,this.reportContainer.style.visibility="visible",r||this.report.render()}catch(t){console.error(t)}this.model.set("_event_data",{event_name:"loaded",event_details:null}),this.touch()})))),this.report.on("rendered",(()=>{console.log("Rendered"),this.model.set("_event_data",{event_name:"rendered",event_details:null}),this.touch()})),this.report.on("error",(t=>{var e;if("visible"===this.reportContainer.style.visibility)return;const r=null===(e=t)||void 0===e?void 0:e.detail;this.model.set("_init_error",`${null==r?void 0:r.message} - ${null==r?void 0:r.detailedMessage}`),this.touch()})),this.model.set("_embedded",!0),this.touch()}exportVisualDataRequestChanged(){return i(this,void 0,void 0,(function*(){if(!this.report)return void this.logError(c);const t=this.model.get("_export_visual_data_request");if(!(t.pageName||t.visualName||t.rows||t.exportDataType))return;if(!t.pageName||!t.visualName){const t="Page and visual names are required";return void this.logError(t)}const e=t.pageName,r=t.visualName,i=t.rows,a=t.exportDataType;try{const t=yield s.getRequestedPage(this.report,e),o=(yield t.getVisuals()).filter((t=>t.name===r))[0];if(!o)throw"Visual not found";const n=yield o.exportData(a,i);this.model.set("_visual_data",n.data),this.touch()}catch(t){this.logError(t)}}))}getFiltersRequestChanged(){return i(this,void 0,void 0,(function*(){if(this.report){if(this.model.get("_get_filters_request"))try{const t=yield this.report.getFilters();if(!t)throw"No filters available";this.model.set("_report_filters",t),this.model.set("_get_filters_request",!1),this.touch()}catch(t){this.logError(t)}}else this.logError(c)}))}reportFiltersChanged(){return i(this,void 0,void 0,(function*(){if(!this.report)return void this.logError(c);const t=this.model.get("_report_filters_request");if(!t.request_completed)try{t.filters.length>0?yield this.report.updateFilters(n.models.FiltersOperations.Replace,t.filters):yield this.report.updateFilters(n.models.FiltersOperations.RemoveAll),this.model.set("_report_filters_request",u),this.touch()}catch(t){this.logError(t)}}))}getPagesRequestChanged(){return i(this,void 0,void 0,(function*(){if(this.report){if(this.model.get("_get_pages_request"))try{const t=yield this.report.getPages();if(!t)throw"Pages not found";const e=t.map((t=>{const{report:e}=t;return a(t,["report"])}));this.model.set("_report_pages",e),this.touch()}catch(t){this.logError(t)}}else this.logError(c)}))}getVisualsPageNameChanged(){return i(this,void 0,void 0,(function*(){if(!this.report)return void this.logError(c);const t=this.model.get("_get_visuals_page_name");if(t)try{const e=yield s.getRequestedPage(this.report,t),r=yield e.getVisuals();if(!r)throw"Visuals not found";const i=r.map((t=>{const{page:e}=t;return a(t,["page"])}));this.model.set("_page_visuals",i),this.touch()}catch(t){this.logError(t)}}))}reportBookmarkNameChanged(){return i(this,void 0,void 0,(function*(){if(!this.report)return void this.logError(c);const t=this.model.get("_report_bookmark_name");try{yield this.report.bookmarksManager.apply(t)}catch(t){this.logError(t)}}))}getBookmarksRequestChanged(){return i(this,void 0,void 0,(function*(){if(this.report){if(this.model.get("_get_bookmarks_request"))try{const t=yield this.report.bookmarksManager.getBookmarks();0===t.length?(this.model.set("_report_bookmarks",[""]),this.touch()):(this.model.set("_report_bookmarks",t),this.touch())}catch(t){this.logError(t)}}else this.logError(c)}))}reportActivePageChanged(){return i(this,void 0,void 0,(function*(){if(!this.report)return void this.logError(c);const t=this.model.get("_report_active_page");try{yield this.report.setPage(t)}catch(t){this.logError(t)}}))}logError(t){let e=JSON.stringify(t);"{}"===e&&(e=t.toString()),console.error(t),this.model.set("_client_error",e),this.touch()}}e.ReportView=f},593:function(t,e,r){"use strict";var i=this&&this.__awaiter||function(t,e,r,i){return new(r||(r=Promise))((function(a,o){function n(t){try{s(i.next(t))}catch(t){o(t)}}function l(t){try{s(i.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?a(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(n,l)}s((i=i.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.getTokenExpirationTimeout=e.setTokenExpirationListener=e.getRequestedPage=e.getActivePageSize=e.powerbi=void 0;const a=r(957),o=r(412);e.powerbi=new a.service.Service(a.factories.hpmFactory,a.factories.wpmpFactory,a.factories.routerFactory,{type:"powerbi-jupyter",sdkWrapperVersion:o.MODULE_VERSION});function n(t){const e=function(t){if(t)try{const e=function(t){let e;e=t.split(".")[1].replace(/-/g,"+").replace(/_/g,"/");var r=decodeURIComponent(atob(e).split("").map((function(t){return"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)})).join(""));return JSON.parse(r)}(t);return null==e?void 0:e.exp}catch(t){return}}(t);return e?1e3*e-Date.now()-6e5:0}e.getActivePageSize=function(t){return i(this,void 0,void 0,(function*(){const e=(yield t.getPages()).find((t=>t.isActive));if(!e)throw"No active report page";return e.defaultSize}))},e.getRequestedPage=function(t,e){return i(this,void 0,void 0,(function*(){const r=(yield t.getPages()).filter((t=>t.name===e))[0];if(!r)throw"Page not found";return r}))},e.setTokenExpirationListener=function(t,e){const r=n(t);r<=0?e.setTokenExpiredFlag():(console.log("Access Token will expire in "+r+" milliseconds."),setTimeout((()=>{e.setTokenExpiredFlag()}),r))},e.getTokenExpirationTimeout=n},412:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MODULE_NAME=e.MODULE_VERSION=void 0;const i=r(147);e.MODULE_VERSION=i.version,e.MODULE_NAME=i.name},146:e=>{"use strict";e.exports=t},147:t=>{"use strict";t.exports=JSON.parse('{"name":"powerbi-jupyter-client","version":"3.1.1","description":"A Custom Jupyter Widget Library","keywords":["jupyter","jupyterlab","jupyterlab-extension","widgets"],"files":["lib/**/*.js","dist/*.js","dist/*.js.map","css/*.css"],"homepage":"https://github.com/Microsoft/powerbi-jupyter","bugs":{"url":"https://github.com/Microsoft/powerbi-jupyter/issues"},"license":"MIT","author":{"name":"Microsoft"},"main":"lib/index.js","types":"./lib/index.d.ts","repository":{"type":"git","url":"https://github.com/Microsoft/powerbi-jupyter"},"scripts":{"build":"npm run build:lib && npm run build:nbextension","build:labextension":"npm run clean:labextension && mkdirp powerbiclient/labextension && cd powerbiclient/labextension && npm pack ../..","build:lib":"tsc","build:nbextension":"webpack --mode production","build:all":"npm run build:labextension && npm run build:nbextension","clean":"npm run clean:lib && npm run clean:nbextension","clean:lib":"rimraf lib","clean:labextension":"rimraf powerbiclient/labextension","clean:nbextension":"rimraf powerbiclient/nbextension/static/index.js","lint":"eslint . --ext .ts,.tsx --fix","lint:check":"eslint . --ext .ts,.tsx","pretest:common":"webpack --config tests/webpack.config.js","test":"npm run test:chrome","test:chrome":"npm run pretest:common && karma start --browsers=Chrome tests/karma.conf.js","test:debug":"npm run pretest:common && karma start --browsers=Chrome --singleRun=false --debug=true tests/karma.conf.js","test:firefox":"npm run pretest:common && karma start --browsers=Firefox tests/karma.conf.js","test:ie":"npm run pretest:common && karma start --browsers=IE tests/karma.conf.js","watch":"npm-run-all -p watch:*","watch:lib":"tsc -w","watch:nbextension":"webpack --watch"},"dependencies":{"@jupyter-widgets/base":"^4.1.0","powerbi-client":"^2.22.0"},"devDependencies":{"@phosphor/application":"^1.6.0","@phosphor/widgets":"^1.6.0","@types/expect.js":"^0.3.29","@types/mocha":"^5.2.5","@types/node":"^10.11.6","@types/webpack-env":"^1.13.6","@typescript-eslint/eslint-plugin":"^3.6.0","@typescript-eslint/parser":"^3.6.0","acorn":"^7.2.0","css-loader":"^3.2.0","eslint":"^7.4.0","eslint-config-prettier":"^6.11.0","eslint-plugin-prettier":"^3.1.4","expect.js":"^0.3.1","fs-extra":"^7.0.0","karma":"^6.3.17","karma-chrome-launcher":"^2.2.0","karma-firefox-launcher":"^1.1.0","karma-ie-launcher":"^1.0.0","karma-mocha":"^2.0.1","karma-mocha-reporter":"^2.2.5","karma-typescript":"^5.0.3","karma-typescript-es6-transform":"^5.0.3","mkdirp":"^0.5.1","mocha":"^9.2.2","npm-run-all":"^4.1.3","prettier":"^2.0.5","rimraf":"^2.6.2","source-map-loader":"^0.2.4","style-loader":"^1.0.0","ts-loader":"^5.2.1","typescript":"~4.3.5","webpack":"^5.71.0","webpack-cli":"^4.10.0"},"jupyterlab":{"extension":"lib/plugin"}}')}},r={};function i(t){var a=r[t];if(void 0!==a)return a.exports;var o=r[t]={id:t,exports:{}};return e[t].call(o.exports,o,o.exports,i),o.exports}return i.nc=void 0,i(112)})()));
//# sourceMappingURL=index.js.map